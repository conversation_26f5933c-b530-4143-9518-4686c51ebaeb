#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试政策强度评分系统
验证6维度评分规则的正确性
"""

from complete_policy_extractor import CompletePolicyExtractor
import pandas as pd

def test_target_dimension():
    """测试目标维度评分"""
    print("=== 测试目标维度评分 ===")
    
    extractor = CompletePolicyExtractor()
    
    test_cases = [
        {
            'text': '加强环保工作，推进绿色发展。',
            'expected': 0.5,
            'description': '间接给出低碳发展目标'
        },
        {
            'text': '制定低碳发展目标，实现节能减排目标。',
            'expected': 0.75,
            'description': '直接给出低碳发展目标'
        },
        {
            'text': '到2025年碳排放强度下降18%，实现低碳目标。',
            'expected': 1.0,
            'description': '直接给出低碳发展目标且有具体数值'
        },
        {
            'text': '加强经济建设，提高GDP增长率。',
            'expected': 0.0,
            'description': '未给出低碳发展目标'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        score = extractor.score_target_dimension(case['text'])
        status = "✓" if abs(score - case['expected']) < 0.01 else "✗"
        print(f"  测试{i}: {status} 得分={score:.2f} 期望={case['expected']} - {case['description']}")

def test_scope_groups():
    """测试范围-目标群体评分"""
    print("\n=== 测试范围-目标群体评分 ===")
    
    extractor = CompletePolicyExtractor()
    
    test_cases = [
        {
            'text': '适用于工业企业。',
            'expected': 0.166,
            'description': '只针对一类群体(企业)'
        },
        {
            'text': '适用于企业和个人用户。',
            'expected': 0.332,
            'description': '针对两类群体'
        },
        {
            'text': '适用于企业、个人、政府部门。',
            'expected': 0.498,
            'description': '针对三类群体'
        },
        {
            'text': '适用于企业、个人、政府部门、行业协会。',
            'expected': 0.5,
            'description': '针对所有目标群体'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        score = extractor.score_scope_groups(case['text'])
        status = "✓" if abs(score - case['expected']) < 0.01 else "✗"
        print(f"  测试{i}: {status} 得分={score:.3f} 期望={case['expected']} - {case['description']}")

def test_scope_problems():
    """测试范围-问题类型评分"""
    print("\n=== 测试范围-问题类型评分 ===")
    
    extractor = CompletePolicyExtractor()
    
    test_cases = [
        {
            'text': '解决工业污染问题。',
            'expected': 0.125,
            'description': '仅针对一种问题'
        },
        {
            'text': '解决工业污染和燃煤污染问题。',
            'expected': 0.25,
            'description': '针对两种问题'
        },
        {
            'text': '解决燃煤、工业、机动车、扬尘、农业污染防治问题。',
            'expected': 0.5,
            'description': '针对所有问题类型'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        score = extractor.score_scope_problems(case['text'])
        status = "✓" if abs(score - case['expected']) < 0.01 else "✗"
        print(f"  测试{i}: {status} 得分={score:.3f} 期望={case['expected']} - {case['description']}")

def test_budget_dimension():
    """测试预算维度评分"""
    print("\n=== 测试预算维度评分 ===")
    
    extractor = CompletePolicyExtractor()
    
    test_cases = [
        {
            'text': '加强环保工作。',
            'expected': 0.0,
            'description': '没有提到预算'
        },
        {
            'text': '设立专项资金支持项目建设。',
            'expected': 0.5,
            'description': '提到预算但没有具体金额'
        },
        {
            'text': '设立专项资金5000万元支持项目建设。',
            'expected': 1.0,
            'description': '提到预算且给出具体金额'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        score = extractor.score_budget_dimension(case['text'])
        status = "✓" if abs(score - case['expected']) < 0.01 else "✗"
        print(f"  测试{i}: {status} 得分={score:.2f} 期望={case['expected']} - {case['description']}")

def test_execution_dimension():
    """测试执行维度评分"""
    print("\n=== 测试执行维度评分 ===")
    
    extractor = CompletePolicyExtractor()
    
    test_cases = [
        {
            'text': '加强环保工作。',
            'expected': 0.0,
            'description': '没有执行说明'
        },
        {
            'text': '由环保部门负责执行相关规定。',
            'expected': 0.25,
            'description': '有执行主体和规则说明'
        },
        {
            'text': '由环保部门统一负责执行。',
            'expected': 0.5,
            'description': '有执行主体+单一主体执行'
        },
        {
            'text': '由环保部门统一负责，严格按照规定执行。',
            'expected': 0.75,
            'description': '有执行主体+单一主体+严格程序'
        },
        {
            'text': '由环保部门统一负责，严格按照规定执行，违反者处以罚款。',
            'expected': 1.0,
            'description': '四个方面都有'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        score = extractor.score_execution_dimension(case['text'])
        status = "✓" if abs(score - case['expected']) < 0.01 else "✗"
        print(f"  测试{i}: {status} 得分={score:.2f} 期望={case['expected']} - {case['description']}")

def test_supervision_dimension():
    """测试监督维度评分"""
    print("\n=== 测试监督维度评分 ===")
    
    extractor = CompletePolicyExtractor()
    
    test_cases = [
        {
            'text': '加强环保工作。',
            'expected': 0.0,
            'description': '无监督'
        },
        {
            'text': '各部门要加强自查工作。',
            'expected': 0.5,
            'description': '内部监督'
        },
        {
            'text': '建立第三方监督机制。',
            'expected': 1.0,
            'description': '外部专门机构监督'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        score = extractor.score_supervision_dimension(case['text'])
        status = "✓" if abs(score - case['expected']) < 0.01 else "✗"
        print(f"  测试{i}: {status} 得分={score:.2f} 期望={case['expected']} - {case['description']}")

def test_comprehensive_cases():
    """测试综合案例"""
    print("\n=== 测试综合案例 ===")
    
    extractor = CompletePolicyExtractor()
    
    # 高强度政策示例
    high_intensity_policy = """
    碳达峰碳中和行动方案
    
    一、总体目标
    到2030年实现碳达峰，单位GDP碳排放强度较2005年下降65%以上。
    
    二、适用范围
    本方案适用于全市所有工业企业、个人用户、各级政府机关、行业协会等机构。
    
    三、重点任务
    1. 燃煤锅炉清洁化改造
    2. 工业污染深度治理
    3. 交通运输结构优化
    4. 建筑扬尘治理
    5. 农业面源污染防治
    
    四、保障措施
    市财政设立碳中和专项资金50亿元，对重点项目给予补贴。
    
    五、组织实施
    成立市碳达峰碳中和工作领导小组，统一组织实施。
    建立严格的目标责任制，必须按照规定执行。
    对未完成目标任务的，严肃追究责任，给予党纪政务处分。
    
    六、监督评估
    委托第三方专业机构开展年度评估，建立社会监督机制。
    """
    
    result = extractor.calculate_policy_intensity(high_intensity_policy, "HIGH_INTENSITY")
    
    print(f"高强度政策测试:")
    print(f"  总分: {result['total_score']:.2f}")
    print(f"  强度等级: {result['intensity_level']}")
    print(f"  各维度得分:")
    for dim, score in result['scores'].items():
        print(f"    {dim}: {score:.2f}")
    
    # 低强度政策示例
    low_intensity_policy = """
    关于环保工作的通知
    
    各单位要重视环境保护工作，加强宣传教育。
    相关部门要做好协调配合。
    """
    
    result = extractor.calculate_policy_intensity(low_intensity_policy, "LOW_INTENSITY")
    
    print(f"\n低强度政策测试:")
    print(f"  总分: {result['total_score']:.2f}")
    print(f"  强度等级: {result['intensity_level']}")
    print(f"  各维度得分:")
    for dim, score in result['scores'].items():
        print(f"    {dim}: {score:.2f}")

def test_with_original_data():
    """使用原项目数据进行测试"""
    print("\n=== 使用原项目数据测试 ===")
    
    try:
        # 尝试读取原项目的政策文本
        with open('policy texts (10 policies for coding)/city_1.txt', 'r', encoding='utf-8') as f:
            city_1_text = f.read()
        
        extractor = CompletePolicyExtractor()
        result = extractor.calculate_policy_intensity(city_1_text, "CITY_1")
        
        print(f"常州市政策分析结果:")
        print(f"  总分: {result['total_score']:.2f}")
        print(f"  强度等级: {result['intensity_level']}")
        print(f"  各维度得分:")
        for dim, score in result['scores'].items():
            print(f"    {dim}: {score:.2f}")
            
    except FileNotFoundError:
        print("  未找到原项目政策文本文件，跳过此测试")

def main():
    """运行所有测试"""
    print("政策强度评分系统测试")
    print("=" * 50)
    
    # 运行各维度测试
    test_target_dimension()
    test_scope_groups()
    test_scope_problems()
    test_budget_dimension()
    test_execution_dimension()
    test_supervision_dimension()
    
    # 运行综合测试
    test_comprehensive_cases()
    
    # 使用原项目数据测试
    test_with_original_data()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n评分规则验证:")
    print("✓ 目标维度: 0(无) → 0.5(间接) → 0.75(直接) → 1.0(直接+数值)")
    print("✓ 范围-群体: 0(单一) → 每增加一个群体+0.166 → 0.5(全部4个)")
    print("✓ 范围-问题: 0(单一) → 每增加一个问题+0.125 → 0.5(全部5个)")
    print("✓ 预算维度: 0(无) → 0.5(提及) → 1.0(具体金额)")
    print("✓ 执行维度: 0(无) → 四个方面各0.25分 → 1.0(全部)")
    print("✓ 监督维度: 0(无) → 0.5(内部) → 1.0(外部专门机构)")

if __name__ == "__main__":
    main()
