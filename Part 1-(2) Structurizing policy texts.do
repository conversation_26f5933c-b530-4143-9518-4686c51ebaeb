cd "C:\...\Low_carbon_policy_intensity_code\policy texts (10 policies for coding)"

*Deal with files with headings and subheadings. 
*Files without headings will cut the circulation, please drop the file number and rerun the code.
foreach i of numlist 1 3/5 7 9/10{
    import excel city_`i'.txt_new.xlsx, sheet("Sheet1") firstrow
	drop A
	replace title = title[_n-1] if title ==""

	*deal with headings with "、"
	split title, p("。")
	keep title content title1
	split title1, p("、")
	replace title11="" if title12==""
	gen titlenew2= title11+"、"+ title12
	drop title11
	drop title12
	
	gen titlenew21=titlenew2 if strmatch(titlenew2,"*一、*")==1 | strmatch(titlenew2,"*二、*")==1 | strmatch(titlenew2,"*三、*")==1 | strmatch(titlenew2,"*四、*")==1 | strmatch(titlenew2,"*五、*")==1 | strmatch(titlenew2,"*六、*")==1 | strmatch(titlenew2,"*七、*")==1 | strmatch(titlenew2,"*八、*")==1 | strmatch(titlenew2,"*九、*")==1 | strmatch(titlenew2,"*十、*")==1 | strmatch(titlenew2,"*十一、*")==1 | strmatch(titlenew2,"*十二、*")==1 | strmatch(titlenew2,"*十三、*")==1 | strmatch(titlenew2,"*十四、*")==1 | strmatch(titlenew2,"*十五、*")==1 | strmatch(titlenew2,"*十六、*")==1 | strmatch(titlenew2,"*十七、*")==1 | strmatch(titlenew2,"*十八、*")==1 | strmatch(titlenew2,"*十九、*")==1 | strmatch(titlenew2,"*二十、*")==1
	replace titlenew21=title1 if titlenew21 !=""
	replace titlenew21 = titlenew21[_n-1] if titlenew21 ==""
	drop titlenew2

	*deal with headings with（）
	gen title_fine=title1
	split title_fine, p("）")
	replace title_fine1="" if title_fine2==""
	gen titlenew1= title_fine1+"）"+ title_fine2
	replace titlenew1="" if titlenew1=="）"
	drop title_fine1
	drop title_fine2

	gen titlenew11=titlenew1 if strmatch(titlenew1,"*（一）*")==1 | strmatch(titlenew1,"*（二）*")==1 | strmatch(titlenew1,"*（三）*")==1 | strmatch(titlenew1,"*（四）*")==1 | strmatch(titlenew1,"*（五）*")==1 | strmatch(titlenew1,"*（六）*")==1 | strmatch(titlenew1,"*（七）*")==1 | strmatch(titlenew1,"*（八）*")==1 | strmatch(titlenew1,"*（九）*")==1 | strmatch(titlenew1,"*（十）*")==1 | strmatch(titlenew1,"*（十一）*")==1 | strmatch(titlenew1,"*（十二）*")==1 | strmatch(titlenew1,"*（十三）*")==1 | strmatch(titlenew1,"*（十四）*")==1 | strmatch(titlenew1,"*（十五）*")==1 | strmatch(titlenew1,"*（十六）*")==1 | strmatch(titlenew1,"*（十七）*")==1 | strmatch(titlenew1,"*（十八）*")==1 | strmatch(titlenew1,"*（十九）*")==1 | strmatch(titlenew1,"*（二十）*")==1
	drop titlenew1

	*fill titles by group
	gen n=_n
	bysort titlenew21: egen min=min(n)
	sort n
	gen new1=n-min+1
	replace titlenew11 = titlenew11[_n-1] if titlenew11 =="" & new1 !=1
	order titlenew21, after(title)
	order titlenew11, after(titlenew21)
	
	save city_`i'.dta,replace
	clear
}

*Deal with files without headings and subheadings
foreach i of numlist 2 6 8{
	import excel city_`i'.txt_new.xlsx, sheet("Sheet1") firstrow
	drop if A==.
	drop A
	save city_`i'.dta,replace
	clear
}
