{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Code for \"China’s low-carbon policy intensity dataset from national- to prefecture-level over 2007-2022\"\n", "* This code is written and executed by Python 3.10.8 and Stata 15.\n", "\n", "* The working folder of this code is \"Low_carbon_policy_intensity_code\", please check the working directory before execution."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Part 1: Data preparation\n", "* Processes of downloading policy texts and renaming files are omitted in this code."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["city_1.txt 31 32\n", "city_10.txt 75 92\n", "city_2.txt 82 85\n", "city_3.txt 33 33\n", "city_4.txt 95 105\n", "city_5.txt 19 19\n", "city_6.txt 21 22\n", "city_7.txt 12 12\n", "city_8.txt 6 6\n", "city_9.txt 27 27\n"]}], "source": ["# ----------- (1) Structurizing policy texts (from .txt to .xlsx) _Python 3.10.8------------\n", "\n", "# Note: The contents and heading&subheading in some excels are not 100% matched, which requires manual check. \n", "#(see the cell output and folder-\"output/samples for manual adjustion\" for details)\n", "\n", "import re\n", "import os\n", "import pandas as pd\n", "\n", "path = './policy texts (10 policies for coding)/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "# Define function-\"get_title\" to extract headings and subheadings in plain files\n", "def get_title(policy):\n", "    with open(path +  policy, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        lines = f.readlines()\n", "        policy = lines[2]\n", "        content = \"\\u3000\\u3000\".join(lines)\n", "        a=content.split(\"\\u3000\")\n", "        content1 = \"\\n\".join(a)\n", "        b=content1.split(\"\\n\")\n", "        #keywords for headings and subheadings\n", "        regex = re.compile(r'（\\d）|\\d\\、|10、|11、|12、|13、|14、|15、|16、|17、|18、|19、|20、|21、|22、|23、|24、|25、|26、|27、|28、|29、|30、|31、|32、|33、|34、|35、|一、|二、|三、|四、|五、|六、|七、|八、|九、|十、|十一、|十二、|十三、|十四、|十五、|十六、|十七、|十八、|十九、|二十、| \\\n", "        二十、|二十一、|二十二、|二十三、|二十四、|二十五、|二十六、|二十七、|二十八、|二十九、|三十、|三十一、|三十二、|三十三、|三十四、|三十五、|三十六、|三十七、|三十八、|三十九、|四十、|四十一、|四十二、|四十三、| \\\n", "        四十四、|四十五、|四十六、|四十七、|四十八、|四十九、|五十、|（一）|（二）|（三）|（四）|（五）|（六）|（六）|（七）|（八）|（九）|（十）|（十一）|（十二）|（十三）|（十四）|（十五）|（十六）| \\\n", "        （十七）|（十八）|（十九）|（二十）|第一节|第二节|第三节|第四节|第五节|第六节|第七节|第八节|第九节|第十节|第十一节|第十二节| \\\n", "        第十三节|第十四节|第十五节|第十六节|第十七节|第十八节|第十九节|第二十节|第一章|第二章|第三章|第四章|第五章|第六章| \\\n", "        第七章|第八章|第九章|第十章|第十一章|第十二章|第十三章|第十四章|第十五章|第十六章|第十七章|第十八章|第十九章|第二十章')\n", "        #print(regex)\n", "        data=[]\n", "        for x in b:\n", "            x = x.strip()\n", "            if re.match(regex, x) != None:\n", "                data.append(x)\n", "        data.insert(0, 'None')\n", "        return policy, data\n", "\n", "# Define function-\"get_content\" to extract contents of each subheading in plain files\n", "def get_content(policy):\n", "    word = ''\n", "    with open(path + policy, 'r', encoding=\"utf-8\",errors='ignore') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            line.split(\"\\u3000\")\n", "            line.split('\\n')\n", "            line = line.replace(\" \",\"\")\n", "            word += line\n", "    #keywords for headings and subheadings\n", "    new =re.split(r'（\\d）|1、|2、|3、|4、|5、|6、|7、|8、|9、|10、|11、|12、|13、|14、|15、|16、|17、|18、|19、|20、|21、|22、|23、|24、|25、|26、|27、|28、|29、|30、|31、|32、|33、|34、|35、|一、|二、|三、|四、|五、|六、|七、|八、|九、|十、|十一、|十二、|十三、|十四、|十五、|十六、|十七、|十八、|十九、|二十、| \\\n", "    二十、|（一）|（二）|（三）|（四）|（五）|（六）|（六）|（七）|（八）|（九）|（十）|（十一）|（十二）|（十三）|（十四）|（十五）|（十六）| \\\n", "    （十七）|（十八）|（十九）|（二十）|第一节|第二节|第三节|第四节|第五节|第六节|第七节|第八节|第九节|第十节|第十一节|第十二节| \\\n", "    第十三节|第十四节|第十五节|第十六节|第十七节|第十八节|第十九节|第二十节|第一章|第二章|第三章|第四章|第五章|第六章| \\\n", "    第七章|第八章|第九章|第十章|第十一章|第十二章|第十三章|第十四章|第十五章|第十六章|第十七章|第十八章|第十九章|第二十章', word)\n", "    while None in new:\n", "        new.remove(None)\n", "    regex = re.compile(r'^\\d\\.')\n", "    filtered = [i for i in new if not regex.match(i)]\n", "    return filtered\n", "\n", "# Append title and content into one excel file\n", "for filename in path_list:\n", "    policy, title = get_title(filename)\n", "    filtered = get_content(filename)\n", "    df1 = pd.DataFrame(title, columns = ['title'])\n", "    df2 = pd.DataFrame(filtered, columns = ['content'])\n", "    A = pd.concat([df1, df2], axis = 1)\n", "    print(filename, len(title), len(filtered))\n", "    A.to_excel(os.path.join(path, filename + \"_new.xlsx\"), engine='xlsxwriter')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (2) Structurizing policy texts (from .xlsx to .dta) _Stata 15------------\n", "\n", "#This part needs to be executed in Stata 15.\n", "#The do file version (\"Part 1-(2) Structurizing policy texts.do\") for Stata 15 is saved in Folder-\"Low_carbon_policy_intensity_code\".\n", "\n", "''' \n", "cd \"C:\\...\\Low_carbon_policy_intensity_code\\policy texts (10 policies for coding)\"\n", "\n", "*Deal with files with headings and subheadings. \n", "*Files without headings will cut the circulation, please drop the file number and rerun the code.\n", "foreach i of numlist 1 3/5 7 9/10{\n", "    import excel city_`i'.txt_new.xlsx, sheet(\"Sheet1\") firstrow\n", "\tdrop A\n", "\treplace title = title[_n-1] if title ==\"\"\n", "\n", "\t*deal with headings with \"、\"\n", "\tsplit title, p(\"。\")\n", "\tkeep title content title1\n", "\tsplit title1, p(\"、\")\n", "\treplace title11=\"\" if title12==\"\"\n", "\tgen titlenew2= title11+\"、\"+ title12\n", "\tdrop title11\n", "\tdrop title12\n", "\t\n", "\tgen titlenew21=titlenew2 if strmatch(titlenew2,\"*一、*\")==1 | strmatch(titlenew2,\"*二、*\")==1 | strmatch(titlenew2,\"*三、*\")==1 | strmatch(titlenew2,\"*四、*\")==1 | strmatch(titlenew2,\"*五、*\")==1 | strmatch(titlenew2,\"*六、*\")==1 | strmatch(titlenew2,\"*七、*\")==1 | strmatch(titlenew2,\"*八、*\")==1 | strmatch(titlenew2,\"*九、*\")==1 | strmatch(titlenew2,\"*十、*\")==1 | strmatch(titlenew2,\"*十一、*\")==1 | strmatch(titlenew2,\"*十二、*\")==1 | strmatch(titlenew2,\"*十三、*\")==1 | strmatch(titlenew2,\"*十四、*\")==1 | strmatch(titlenew2,\"*十五、*\")==1 | strmatch(titlenew2,\"*十六、*\")==1 | strmatch(titlenew2,\"*十七、*\")==1 | strmatch(titlenew2,\"*十八、*\")==1 | strmatch(titlenew2,\"*十九、*\")==1 | strmatch(titlenew2,\"*二十、*\")==1\n", "\treplace titlenew21=title1 if titlenew21 !=\"\"\n", "\treplace titlenew21 = titlenew21[_n-1] if titlenew21 ==\"\"\n", "\tdrop titlenew2\n", "\n", "\t*deal with headings with（）\n", "\tgen title_fine=title1\n", "\tsplit title_fine, p(\"）\")\n", "\treplace title_fine1=\"\" if title_fine2==\"\"\n", "\tgen titlenew1= title_fine1+\"）\"+ title_fine2\n", "\treplace titlenew1=\"\" if titlenew1==\"）\"\n", "\tdrop title_fine1\n", "\tdrop title_fine2\n", "\n", "\tgen titlenew11=titlenew1 if strmatch(titlenew1,\"*（一）*\")==1 | strmatch(titlenew1,\"*（二）*\")==1 | strmatch(titlenew1,\"*（三）*\")==1 | strmatch(titlenew1,\"*（四）*\")==1 | strmatch(titlenew1,\"*（五）*\")==1 | strmatch(titlenew1,\"*（六）*\")==1 | strmatch(titlenew1,\"*（七）*\")==1 | strmatch(titlenew1,\"*（八）*\")==1 | strmatch(titlenew1,\"*（九）*\")==1 | strmatch(titlenew1,\"*（十）*\")==1 | strmatch(titlenew1,\"*（十一）*\")==1 | strmatch(titlenew1,\"*（十二）*\")==1 | strmatch(titlenew1,\"*（十三）*\")==1 | strmatch(titlenew1,\"*（十四）*\")==1 | strmatch(titlenew1,\"*（十五）*\")==1 | strmatch(titlenew1,\"*（十六）*\")==1 | strmatch(titlenew1,\"*（十七）*\")==1 | strmatch(titlenew1,\"*（十八）*\")==1 | strmatch(titlenew1,\"*（十九）*\")==1 | strmatch(titlenew1,\"*（二十）*\")==1\n", "\tdrop titlenew1\n", "\n", "\t*fill titles by group\n", "\tgen n=_n\n", "\tbysort titlenew21: egen min=min(n)\n", "\tsort n\n", "\tgen new1=n-min+1\n", "\treplace titlenew11 = titlenew11[_n-1] if titlenew11 ==\"\" & new1 !=1\n", "\torder titlenew21, after(title)\n", "\torder titlenew11, after(titlenew21)\n", "\t\n", "\tsave city_`i'.dta,replace\n", "\tclear\n", "}\n", "\n", "*Deal with files without headings and subheadings\n", "foreach i of numlist 2 6 8{\n", "\timport excel city_`i'.txt_new.xlsx, sheet(\"Sheet1\") firstrow\n", "\tdrop if A==.\n", "\tdrop A\n", "\tsave city_`i'.dta,replace\n", "\tclear\n", "}\n", "\n", "'''\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (3) Disaggregating into policy objective and instrument files (from .dta to .txt) _Stata 15------------\n", "\n", "#This part needs to be executed in Stata 15.\n", "#The do file version (\"Part 1-(3) Disaggregating policy objective and instrument.do\") for Stata 15 is saved in Folder-\"Low_carbon_policy_intensity_code\".\n", "\n", "''' \n", "cd \"C:\\...\\Low_carbon_policy_intensity_code\\policy texts (10 policies for coding)\"\n", "\n", "*********************************************1. For policies with headings and subheadings*********************************************\n", "\n", "foreach u of numlist 1 3 4 5 7 9 10{\n", "\tuse city_`u'.dta\n", "\tgen mark1=0\n", "\t*mark1: Headings with keywords for policy objective：\n", "\treplace mark1=1 if strmatch(titlenew21 ,\"*目标*\")==1 | strmatch(titlenew21 ,\"*任务*\")==1 | strmatch(titlenew21 ,\"*指标*\")==1 | strmatch(titlenew21 ,\"*产业政策导向*\")==1\n", "\tgen mark11=0\n", "\t*mark11: Subheadings with keywords for policy objective\n", "\treplace mark11=1 if strmatch(titlenew11 ,\"*目标*\")==1 | strmatch(titlenew11 ,\"*任务*\")==1 | strmatch(titlenew11 ,\"*指标*\")==1| strmatch(titlenew11 ,\"*产业政策导向*\")==1\n", "\tgen mark111=0\n", "\t*mark111: Contents with keywords for policy objective\n", "\treplace mark111=1 if strmatch(content ,\"*目标*\")==1 | strmatch(content ,\"*任务*\")==1 | strmatch(content ,\"*指标*\")==1| strmatch(content,\"*产业政策导向*\")==1\n", "\tegen mark1_p=count(mark1) if mark1==0\n", "\treplace mark1_p = mark1_p[_n-1] if mark1_p ==.\n", "\tgen N=_N\n", "\t*Generate variable \"gap\" for headings: gap==0 means headings do not have keywords for policy objectives.\n", "\tgen gap=N-mark1_p\n", "\treplace gap=1 if gap!=0\n", "\n", "\t*Generate variable \"gap1\" for subheadings: gap1==0 means subheadings do not have keywords for policy objectives.\n", "\tegen mark11_p=count(mark11) if mark11==0\n", "\treplace mark11_p = mark11_p[_n-1] if mark11_p ==.\n", "\tgen gap1=N-mark11_p\n", "\treplace gap1=1 if gap1!=0\n", "\n", "\t\n", "    *Generate variable \"save\" to summarize possible situations.\n", "\tgen save=.\n", "\t*Use mark111 as the criteria to generate policy objective file if neither headings nor subheadings have keywords for policy objectives.\n", "\treplace save=mark111 if gap==0 & gap1==0\n", "\t*Use mark1 to generate policy objective file if headings have keywords for objectives but subheadings do not have.\n", "\treplace save=mark1 if gap !=0 & gap1==0\n", "\t*Use mark1 to generate policy objective file if headings do not have keywords for objectives but subheadings have.\n", "\treplace save=mark11 if gap==0 & gap1!=0\n", "\t*Use the union set of mark1 and mark11 to generate policy objective file if both headings and subheadings have keywords for objectives.\n", "\tgen mark12=mark1+mark11\n", "\treplace save=mark12 if gap !=0 & gap1!=0\n", "\treplace save=1 if save>1\n", "\t\n", "***Get policy objectives***\n", "\tpreserve\n", "\tkeep if save==1\n", "\t*sort texts of policy objective by subheading, heading, contents\n", "\tgen order_0 = _n\n", "\tgen order=.\n", "\treplace order=1 if mark11==1\n", "\treplace order=2 if mark1==1 & mark11 !=1\n", "\treplace order=3 if mark111==1 & mark1 !=1 & mark11 !=1\n", "\tsort order order_0\n", "\t\n", "\tset obs 2500\n", "\tkeep title titlenew21 titlenew11 content\n", "\tgen new= titlenew21+titlenew11+content\n", "\n", "\tgen check=\"。\"\n", "\tkeep new check\n", "\tduplicates drop new check,force\n", "\texport delimited using G_city_`u'.txt, novarnames replace\n", "\trestore\n", "\t\n", "\t\n", "***Get policy instruments***\n", "\t*drop texts for policy objective\n", "\tdrop if save==1\n", "\tgen mark2=1\n", "\t*drop expressions related to backgrounds\n", "\treplace mark2=0 if strmatch(titlenew21 ,\"*现状*\")==1 | strmatch(titlenew21 ,\"*意义*\")==1| strmatch(titlenew21 ,\"*形势*\")==1 | strmatch(titlenew21 ,\"*指导思想*\")==1 | strmatch(titlenew21 ,\"*基本原则*\")==1 | strmatch(content ,\"*【法宝引证码】*\")==1 | strmatch(titlenew21 ,\"*总体思路*\")==1 | strmatch(titlenew21 ,\"*总体要求*\")==1 | strmatch(titlenew21 ,\"*背景*\")==1\n", "\treplace mark2=0 if strmatch(titlenew11 ,\"*现状*\")==1 | strmatch(titlenew11 ,\"*意义*\")==1| strmatch(titlenew11 ,\"*形势*\")==1 | strmatch(titlenew11 ,\"*指导思想*\")==1 | strmatch(titlenew11 ,\"*基本原则*\")==1 | strmatch(content ,\"*【法宝引证码】*\")==1 | strmatch(titlenew11 ,\"*总体思路*\")==1 | strmatch(titlenew11 ,\"*总体要求*\")==1 | strmatch(titlenew11 ,\"*背景*\")==1\n", "\n", "\tkeep if mark2==1\n", "\tkeep title titlenew21 titlenew11 content\n", "\tgen new= titlenew21+titlenew11+content\n", "    set obs 2500\n", "\tgen check=\"。\"\n", "\tkeep new check\n", "\tduplicates drop new check,force\n", "\texport delimited using A_city_`u'.txt, novarnames replace\n", "\tclear\n", "}\n", "\n", "\n", "*********************************************2. For policies without headings and subheadings*********************************************\n", "\n", "foreach u of numlist 2 6 8 {\n", "\tuse city_`u'.dta\n", "\t*mark111: Contents with keywords for policy objective\n", "\tgen mark111=0\n", "\treplace mark111=1 if strmatch(content ,\"*目标*\")==1 | strmatch(content ,\"*任务*\")==1 | strmatch(content ,\"*指标*\")==1| strmatch(content,\"*产业政策导向*\")==1\n", "\n", "***Get policy objectives***\n", "\tpreserve\n", "\tkeep if mark111==1\n", "\tset obs 2500\n", "\tgen check=\"。\"\n", "\tkeep content check\n", "\tduplicates drop content check,force\n", "\texport delimited using G_city_`u'.txt, novarnames replace\n", "\trestore\n", "\t\n", "***Get policy instruments***\n", "    *drop texts for policy objective\n", "\tdrop if mark111==1\n", "\tgen mark2=1\n", "\t*drop expressions related to backgrounds\n", "\treplace mark2=0 if strmatch(title ,\"*现状*\")==1 | strmatch(title,\"*意义*\")==1| strmatch(title,\"*形势*\")==1 | strmatch(title,\"*指导思想*\")==1 | strmatch(title,\"*基本原则*\")==1 | strmatch(content ,\"*【法宝引证码】*\")==1 | strmatch(title ,\"*总体思路*\")==1 | strmatch(title ,\"*总体要求*\")==1 | strmatch(title,\"*背景*\")==1\n", "\tgen new= title+content\n", "\tset obs 2500\n", "\tgen check=\"。\"\n", "\tkeep new check\n", "\tduplicates drop new check,force\n", "\texport delimited using A_city_`u'.txt, novarnames replace\n", "\tclear\n", "}\n", "'''"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Part 2: Policy classification"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---  new folder...  ---\n", "---  OK  ---\n", "---  new folder...  ---\n", "---  OK  ---\n", "---  new folder...  ---\n", "---  OK  ---\n"]}], "source": ["# -----------Construct new folders for policy contents, objective and instruments _Python 3.10.8------------\n", "import os\n", "\n", "#Define function\"mkdir\" to construct function\n", "def mkdir(path):\n", "\tfolder = os.path.exists(path)\n", "\tif not folder:                   \n", "\t\tos.makedirs(path) \n", "\t\tprint(\"---  new folder...  ---\")\n", "\t\tprint(\"---  OK  ---\")\n", "\telse:\n", "\t\tprint(\"---  There is this folder!  ---\")\n", "\t\t\n", "file1 = \"./policy texts (10 policies for coding)/Full_text\"\n", "file2 = \"./policy texts (10 policies for coding)/Policy_objective\"\n", "file3 = \"./policy texts (10 policies for coding)/Policy_instrument\"\n", "mkdir(file1)\n", "mkdir(file2)\n", "mkdir(file3)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["move ./policy texts (10 policies for coding)\\G_city_1.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_1.txt\n", "move ./policy texts (10 policies for coding)\\G_city_10.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_10.txt\n", "move ./policy texts (10 policies for coding)\\G_city_2.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_2.txt\n", "move ./policy texts (10 policies for coding)\\G_city_3.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_3.txt\n", "move ./policy texts (10 policies for coding)\\G_city_4.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_4.txt\n", "move ./policy texts (10 policies for coding)\\G_city_5.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_5.txt\n", "move ./policy texts (10 policies for coding)\\G_city_6.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_6.txt\n", "move ./policy texts (10 policies for coding)\\G_city_7.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_7.txt\n", "move ./policy texts (10 policies for coding)\\G_city_8.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_8.txt\n", "move ./policy texts (10 policies for coding)\\G_city_9.txt -> ./policy texts (10 policies for coding)/Policy_objective/G_city_9.txt\n", "move ./policy texts (10 policies for coding)\\A_city_1.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_1.txt\n", "move ./policy texts (10 policies for coding)\\A_city_10.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_10.txt\n", "move ./policy texts (10 policies for coding)\\A_city_2.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_2.txt\n", "move ./policy texts (10 policies for coding)\\A_city_3.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_3.txt\n", "move ./policy texts (10 policies for coding)\\A_city_4.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_4.txt\n", "move ./policy texts (10 policies for coding)\\A_city_5.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_5.txt\n", "move ./policy texts (10 policies for coding)\\A_city_6.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_6.txt\n", "move ./policy texts (10 policies for coding)\\A_city_7.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_7.txt\n", "move ./policy texts (10 policies for coding)\\A_city_8.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_8.txt\n", "move ./policy texts (10 policies for coding)\\A_city_9.txt -> ./policy texts (10 policies for coding)/Policy_instrument/A_city_9.txt\n", "move ./policy texts (10 policies for coding)\\city_1.txt -> ./policy texts (10 policies for coding)/Full_text/city_1.txt\n", "move ./policy texts (10 policies for coding)\\city_10.txt -> ./policy texts (10 policies for coding)/Full_text/city_10.txt\n", "move ./policy texts (10 policies for coding)\\city_2.txt -> ./policy texts (10 policies for coding)/Full_text/city_2.txt\n", "move ./policy texts (10 policies for coding)\\city_3.txt -> ./policy texts (10 policies for coding)/Full_text/city_3.txt\n", "move ./policy texts (10 policies for coding)\\city_4.txt -> ./policy texts (10 policies for coding)/Full_text/city_4.txt\n", "move ./policy texts (10 policies for coding)\\city_5.txt -> ./policy texts (10 policies for coding)/Full_text/city_5.txt\n", "move ./policy texts (10 policies for coding)\\city_6.txt -> ./policy texts (10 policies for coding)/Full_text/city_6.txt\n", "move ./policy texts (10 policies for coding)\\city_7.txt -> ./policy texts (10 policies for coding)/Full_text/city_7.txt\n", "move ./policy texts (10 policies for coding)\\city_8.txt -> ./policy texts (10 policies for coding)/Full_text/city_8.txt\n", "move ./policy texts (10 policies for coding)\\city_9.txt -> ./policy texts (10 policies for coding)/Full_text/city_9.txt\n"]}], "source": ["# -----------Move text files for policy contents, objective and instruments to corresponding folder_Python 3.10.8------------\n", "import os\n", "import shutil\n", "from glob import glob\n", "\n", "#Define function to move files to corresponding folders\n", "def mymovefile(srcfile,dstpath):                 \n", "    if not os.path.isfile(srcfile):\n", "        print (\"%s not exist!\"%(srcfile))\n", "    else:\n", "        fpath, fname=os.path.split(srcfile)  \n", "        if not os.path.exists(dstpath):\n", "            os.makedirs(dstpath)                    \n", "        shutil.move(srcfile, dstpath + fname) \n", "        print (\"move %s -> %s\"%(srcfile, dstpath + fname))\n", " \n", "\n", "#Original folder\n", "src_dir = './policy texts (10 policies for coding)/'\n", "#Target forlder\n", "dst_dir1 = './policy texts (10 policies for coding)/Policy_objective/'\n", "dst_dir2 = './policy texts (10 policies for coding)/Policy_instrument/'\n", "dst_dir3 = './policy texts (10 policies for coding)/Full_text/'\n", "\n", "#Corresponding files\n", "src_file_list1 = glob(src_dir + 'G_*') \n", "src_file_list2 = glob(src_dir + 'A_*') \n", "src_file_list3 = glob(src_dir + 'city_*.txt') \n", "\n", "#Move files\n", "for srcfile1 in src_file_list1:\n", "    mymovefile(srcfile1, dst_dir1) \n", "for srcfile2 in src_file_list2:\n", "    mymovefile(srcfile2, dst_dir2) \n", "for srcfile3 in src_file_list3:\n", "    mymovefile(srcfile3, dst_dir3) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 2.0 Lexicon generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (1) merging policies and generating a new .txt file (not provided in the foleder) _Python 3.10.8------------\n", "import os\n", "import re\n", "import os\n", "from zhon.hanzi import punctuation\n", "from LAC import LAC\n", "import pandas as pd\n", "import collections\n", "\n", "#national-level policy texts (not provided in the foleder)\n", "path1 = './texts/full/'\n", "path_list1=os.listdir(path1)\n", "path_list1.sort() \n", "\n", "file=open('./texts/policy_merge.txt','w', encoding='utf8') \n", "\n", "for filename in path_list1:  \n", "    filepath=path1+'/'\n", "    filepath=filepath+filename \n", "    for line in open(filepath, encoding='utf8'):  \n", "        file.writelines(line)  \n", "    file.write('\\n')  \n", "file.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (2) tokenizing policies (not provided in the foleder)_Python 3.10.8------------\n", "f = open(\"./texts/policy_merge.txt\", 'r',encoding='utf-8') \n", "lines = []\n", "for line in f: \n", "    string = re.sub(\"[{}]+\".format(punctuation), \"\", line)\n", "    string1 = string.replace('--', '').replace('[', '')\n", "    lac = LAC(mode = 'seg')\n", "    lac.load_customization('Part 2.1-(1) Objective_content_lexicon.txt', sep=None)\n", "    seg_result = lac.run(string1)\n", "    stopwords = [line.strip() for line in open('Stopwords.txt', 'r', encoding='utf-8').readlines()]\n", "    seg_result1=[w for w in seg_result if w not in stopwords if len(w)>1 and w!='\\t']\n", "    words = []\n", "    for i in seg_result1:\n", "        i = re.sub(\"[\\s+\\.\\!\\/_,$%^*(+\\\"\\'””《》]+|[+——！，。？、~@#￥%……&*（）：；‘]+\", \"\", i)\n", "        if len(i) > 0:\n", "            words.append(i)\n", "    if len(words) > 0:\n", "        lines.append(words)\n", "print(lines[0:5])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (3) saving the result of tokenization (not provided in the foleder)_Python 3.10.8------------\n", "with open('Part 2.0 cutWords_list.txt', 'w', encoding='utf8') as file:\n", "    for cutWords in lines:\n", "        file.write(' '.join(cutWords) + '\\n')\n", "\n", "#open the file\n", "with open('Part 2.0 cutWords_list.txt',encoding=\"utf-8\") as file:\n", "    cutWords_list = [k.split() for k in file.readlines()]\n", "print(cutWords_list[0:10])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[('温室气体', 0.890891969203949), ('核算', 0.8827856779098511), ('清单', 0.8260894417762756), ('能源消费', 0.8177995085716248), ('节能量', 0.7997844815254211), ('所辖', 0.794463038444519), ('配额', 0.7919557690620422), ('通则', 0.7883993983268738), ('国家统计局', 0.7841017246246338), ('碳强度', 0.7822858691215515), ('目标责任制', 0.7813312411308289), ('二氧化碳排放', 0.7765949368476868), ('指标', 0.7729372382164001), ('报告', 0.7709057927131653), ('名录', 0.7694222927093506), ('二氧化', 0.7669311761856079), ('排放', 0.766498327255249), ('统计', 0.7633538842201233), ('行政区', 0.7613207697868347), ('双控', 0.7595624923706055), ('2014', 0.7579082250595093), ('自愿', 0.7535423636436462), ('清缴', 0.752583920955658), ('目标责任', 0.7494224905967712), ('打分', 0.749251127243042), ('地市', 0.748923659324646), ('入海', 0.7476288676261902), ('应当', 0.746965229511261), ('真实性', 0.7462451457977295), ('部门分工', 0.7437520623207092), ('公共机构', 0.7432708740234375), ('二氧化碳排放降低', 0.7423057556152344), ('之外', 0.7407804727554321), ('重化工业', 0.7404689788818359), ('110万吨', 0.7404027581214905), ('亚氮氢氟碳化物', 0.7403790950775146), ('计算', 0.7399301528930664), ('农业源', 0.7395287752151489), ('均为', 0.7394821047782898), ('政绩', 0.7392917275428772), ('行政区域', 0.7387531399726868), ('报表', 0.7386348843574524), ('目标责任考核', 0.7380843758583069), ('评价考核制度', 0.7365549206733704), ('碳汇', 0.7352999448776245), ('管理师', 0.7352775931358337), ('减碳', 0.7348659038543701), ('碳排放权交易', 0.7342803478240967), ('达峰', 0.7334122657775879), ('分步', 0.7329688668251038), ('海口市', 0.7309578657150269), ('自然资源资产离任审计', 0.7307965755462646), ('可行', 0.7307120561599731), ('参考', 0.729781985282898), ('详细', 0.7293978929519653), ('能源消费总量', 0.7290284633636475), ('接口', 0.7276872992515564), ('碳六氟化硫', 0.7272067666053772), ('一级', 0.7270530462265015), ('汇总', 0.7268773913383484), ('碳排放交易', 0.7267869114875793), ('行政区域内', 0.7262932658195496), ('噪声', 0.7250831127166748), ('41号', 0.724521279335022), ('审计', 0.7240646481513977), ('义务', 0.7240085601806641), ('负责人', 0.7238660454750061), ('水效', 0.7229424118995667), ('活动组织行业协会', 0.7224259376525879), ('测定', 0.7215043306350708), ('约束性', 0.7210907936096191), ('基准', 0.721007764339447), ('要商', 0.720963180065155), ('消纳量', 0.7202579975128174), ('公益', 0.719943642616272), ('协议', 0.7186288237571716), ('万家企业', 0.718209981918335), ('对照', 0.7178218364715576), ('审核', 0.7177327275276184), ('分数', 0.7172484993934631), ('用能', 0.7170978784561157), ('领导干部', 0.7163451910018921), ('逐级', 0.7153914570808411), ('总量控制', 0.7153034806251526), ('考虑到', 0.7133177518844604), ('第八条', 0.7132856249809265), ('2017年', 0.7128981351852417), ('自然资源', 0.7124140858650208), ('编制', 0.7120981216430664), ('2013', 0.7120330333709717)], [('烟尘', 0.8170983791351318), ('粉尘', 0.7974985241889954), ('削减', 0.7931176424026489), ('二氧化碳排放强度', 0.7928842902183533), ('主要污染物排放总量', 0.7883421182632446), ('大气污染物排放', 0.7837730050086975), ('氮氧化物', 0.7821801900863647), ('污染物排放', 0.7727841734886169), ('碳六氟化硫', 0.7685524821281433), ('110万吨', 0.7593896985054016), ('持久性', 0.757110595703125), ('二氧化硫', 0.7567702531814575), ('熔化', 0.7543490529060364), ('末端', 0.7516225576400757), ('化学需氧量', 0.7504542469978333), ('消减', 0.7466339468955994), ('用水', 0.7459378242492676), ('电镀', 0.7439155578613281), ('氟化', 0.7434579730033875), ('氨氮', 0.742774486541748), ('VOCs', 0.7417320609092712), ('挥发性', 0.7409564256668091), ('挥发性有机物', 0.740110456943512), ('污染物', 0.7400277256965637), ('COD', 0.7390177249908447), ('有机物', 0.7387532591819763), ('主要污染物', 0.7365731000900269), ('增加值能源消耗', 0.7361605167388916), ('能耗总量', 0.733538806438446), ('己二酸硝酸化肥', 0.7322048544883728), ('纸制品', 0.7321949601173401), ('化石', 0.73170405626297), ('主要污染物排放量', 0.7296221852302551), ('无组织', 0.7293483018875122), ('减少', 0.7256075739860535), ('烟粉尘', 0.7249520421028137), ('亚氮氢氟碳化物', 0.7246775031089783), ('电改袋', 0.7232134938240051), ('有害气体', 0.7224915027618408), ('排放量', 0.7209950089454651), ('大气污染防治', 0.72063809633255), ('新鲜', 0.7196183800697327), ('减轻', 0.7192004919052124), ('30m3', 0.7181748747825623), ('单位能耗', 0.7166290879249573), ('GB3544', 0.71614009141922), ('潜能', 0.7141039967536926), ('能源结构', 0.7133566737174988), ('标准立方米', 0.712648332118988), ('二噁英类', 0.7107980251312256), ('湘江', 0.7107408046722412), ('卤素', 0.7100751996040344), ('SO2', 0.7094412446022034), ('18', 0.7089475393295288), ('铁钢', 0.7081530094146729), ('减量化', 0.7074127197265625), ('氧化亚氮', 0.7073487043380737), ('臭氧层', 0.7071763873100281), ('消耗', 0.7069021463394165), ('耗电', 0.7055256962776184), ('煤场', 0.7037009000778198), ('全氧', 0.7036294341087341), ('源头', 0.702698290348053), ('原辅材料', 0.7025948166847229), ('颗粒物', 0.7022555470466614), ('噪声', 0.7019570469856262), ('无毒', 0.7019539475440979), ('80吨', 0.701903223991394), ('能耗年均降低', 0.7005794048309326), ('涂装', 0.6992335915565491), ('总量', 0.6988396644592285), ('71', 0.6983686089515686), ('产生', 0.6973828077316284), ('有色金属石化', 0.6971684098243713), ('制冷剂', 0.696921706199646), ('防护性', 0.6964459419250488), ('单位工业增加值', 0.6956080198287964), ('轻型化', 0.695489764213562), ('产出', 0.6954746842384338), ('AOX', 0.6950010061264038), ('突发性', 0.6943975687026978), ('358万吨', 0.6942919492721558), ('277万吨', 0.6938590407371521), ('358', 0.6936804056167603), ('有机溶剂', 0.6934694051742554), ('产污', 0.6929145455360413), ('50毫克', 0.6919089555740356), ('能耗降低', 0.6914422512054443), ('涂镀', 0.6914160847663879), ('ODS', 0.6911271810531616)], [('能效', 0.6811280250549316), ('用能', 0.6444510221481323), ('合同能源', 0.6363232731819153), ('用能单位', 0.6288440823554993), ('计量', 0.6093655824661255), ('梯度', 0.5983334183692932), ('风机', 0.597429096698761), ('电动机', 0.5956474542617798), ('节水', 0.5940858721733093), ('拖动', 0.5917695164680481), ('空调', 0.5903981924057007), ('电机', 0.5889044404029846), ('万家企业', 0.5877535939216614), ('公共机构', 0.5864347815513611), ('暖房', 0.5857667922973633), ('铝电解', 0.5856337547302246), ('九大', 0.5844796895980835), ('变频', 0.5842065215110779), ('低碳技术', 0.5815613865852356), ('空压机', 0.581520676612854), ('机电设备', 0.5807834267616272), ('节能降耗', 0.5807656645774841), ('调速', 0.58050137758255), ('水效', 0.5788471698760986), ('用能设备', 0.577929675579071), ('水泵', 0.5756608843803406), ('节能减排', 0.5731297731399536), ('风机泵', 0.5713991522789001), ('集中供热', 0.5712197422981262), ('监察', 0.5701640844345093), ('领跑者', 0.5688958764076233), ('成熟度', 0.5686383843421936), ('认定', 0.5665240287780762), ('节电', 0.5661963820457458), ('窑炉', 0.5651872754096985), ('惠民', 0.564601719379425), ('一级', 0.5642173886299133), ('能源利用效率', 0.5641206502914429), ('粉磨', 0.5637914538383484), ('办公', 0.5633684396743774), ('空调器', 0.5632666349411011), ('器具', 0.5630015134811401), ('节材', 0.5629797577857971), ('节能环保', 0.5627641677856445), ('内燃机', 0.558853805065155), ('万家', 0.5577067732810974), ('压缩机', 0.5560010075569153), ('热量', 0.554425835609436), ('遴选', 0.5519634485244751), ('6000万平方米', 0.5515849590301514), ('电冰箱', 0.5506410002708435), ('革新', 0.5506389737129211), ('家用电器', 0.5500534176826477), ('挖潜', 0.5494970083236694), ('合同', 0.5490729808807373), ('油耗', 0.5490238070487976), ('审计', 0.5490229725837708), ('石灰窑', 0.5488886833190918), ('居住', 0.5487797260284424), ('推荐', 0.5463005304336548), ('2000多家', 0.545754075050354), ('能源效率', 0.5453898906707764), ('星级', 0.5451436042785645), ('既有', 0.5436317324638367), ('锅炉', 0.5430862307548523), ('万种', 0.5424503087997437), ('驾驶', 0.5415862798690796), ('洁具', 0.5414328575134277), ('荧光灯', 0.5413114428520203), ('降耗', 0.5411272048950195), ('轮窑', 0.5411102771759033), ('普及', 0.5403189063072205), ('详细', 0.5397987961769104), ('排量', 0.5394349694252014), ('贯标', 0.5389511585235596), ('夏热冬', 0.5368815064430237), ('能耗', 0.5363934636116028), ('采暖', 0.5357676148414612), ('能源', 0.5341204404830933), ('商厦', 0.5337768197059631), ('基准', 0.5334590077400208), ('目标责任', 0.5315181612968445), ('重点工程', 0.5313188433647156), ('三相异步电动机', 0.5312564969062805), ('节油', 0.529455840587616), ('对标', 0.5289029479026794), ('重点用能单位节能管理', 0.5288764834403992), ('二氧化碳热泵', 0.526928722858429), ('墙材', 0.5267162919044495), ('高效化', 0.5256384611129761)], [('物耗', 0.8528894186019897), ('水耗', 0.8482884764671326), ('单位产品', 0.8199079632759094), ('限额', 0.816737949848175), ('强制性', 0.8096968531608582), ('单位产品能耗', 0.7907711863517761), ('能源消耗', 0.7899388074874878), ('定额', 0.7724629640579224), ('工序', 0.7711777091026306), ('耗能', 0.7679157257080078), ('排放标准', 0.7666753530502319), ('高污染', 0.7666568756103516), ('单耗', 0.764680027961731), ('达标', 0.7514216303825378), ('测定', 0.7510770559310913), ('单位能耗', 0.7461286783218384), ('不到', 0.7411280870437622), ('产污', 0.734181821346283), ('合成氨烧碱', 0.7340843081474304), ('污染物排放', 0.7306616306304932), ('促使', 0.7300879955291748), ('高排放', 0.7296420931816101), ('高耗能', 0.7278879880905151), ('新建项目', 0.7275475263595581), ('门槛', 0.7258115410804749), ('界定', 0.7256805896759033), ('不达标', 0.7250465750694275), ('基准', 0.724902868270874), ('重点用能单位节能管理', 0.7203271985054016), ('取水', 0.7191786170005798), ('机动车', 0.7178979516029358), ('GB3544', 0.7154404520988464), ('主要污染物排放量', 0.713245153427124), ('能源消耗水平', 0.7085188031196594), ('对标', 0.706635057926178), ('用能设备', 0.704671323299408), ('节材', 0.7044677734375), ('烟尘', 0.7028451561927795), ('参差不齐', 0.7015352845191956), ('考核指标', 0.7002375721931458), ('机电设备', 0.6996068954467773), ('低效率', 0.6982374787330627), ('水效', 0.6970133781433105), ('效果好', 0.6967266201972961), ('公约', 0.6966236233711243), ('修正', 0.6948404312133789), ('硫酸钾', 0.6925312876701355), ('出品率', 0.6900548934936523), ('周转量', 0.6898735165596008), ('能耗总量', 0.6892544627189636), ('主要污染物排放', 0.689124584197998), ('清洁生产促进法', 0.688896119594574), ('工作量', 0.6870965957641602), ('比达', 0.6830593347549438), ('指导目录', 0.6824468374252319), ('综合能耗', 0.6805103421211243), ('2008', 0.680229127407074), ('粉尘', 0.6800803542137146), ('选项', 0.6798812747001648), ('一级', 0.6790179014205933), ('空压机', 0.6775966286659241), ('能效', 0.6770980358123779), ('体系制', 0.6767032742500305), ('用水', 0.6754042506217957), ('规程', 0.6746769547462463), ('降耗', 0.6744996905326843), ('效率低', 0.6743205785751343), ('熔化', 0.6721962094306946), ('01', 0.6721698641777039), ('审查', 0.6718457341194153), ('污染物', 0.6713859438896179), ('产品质量', 0.671234130859375), ('未达标', 0.670835554599762), ('营运', 0.670689582824707), ('台阶', 0.6698899865150452), ('1000项', 0.6691372394561768), ('明令', 0.6690258383750916), ('GMP', 0.668586790561676), ('良莠不齐', 0.6673963665962219), ('大效率低', 0.6672012209892273), ('梯度', 0.6665747165679932), ('标杆', 0.6650147438049316), ('重化工业', 0.6649830937385559), ('颁布', 0.6647763848304749), ('增加值能源消耗', 0.6635095477104187), ('三相异步电动机', 0.6634633541107178), ('零极距氧阴极', 0.6631259918212891), ('耗能高', 0.6626462340354919), ('接近', 0.6624929308891296), ('过快增长', 0.6613492965698242)], [('领跑者', 0.8545567393302917), ('对标', 0.8531610369682312), ('水效', 0.8271554708480835), ('基准', 0.8126358389854431), ('标杆', 0.8106867074966431), ('限额', 0.7682784199714661), ('用能', 0.7660505175590515), ('高耗能', 0.7502362728118896), ('机电设备', 0.745880126953125), ('用能设备', 0.7445662021636963), ('一级', 0.7402994632720947), ('工序', 0.7388810515403748), ('强制性', 0.7367510199546814), ('活动组织行业协会', 0.7316555380821228), ('空压机', 0.7299749255180359), ('详细', 0.7185968160629272), ('公共机构', 0.7171587347984314), ('定额', 0.7161126732826233), ('空调', 0.714227557182312), ('万家企业', 0.7127190828323364), ('能源利用效率', 0.71173495054245), ('耗能', 0.7108198404312134), ('电动机', 0.7083442807197571), ('单耗', 0.7073999047279358), ('节材', 0.7066031694412231), ('标识', 0.7062140703201294), ('重点用能单位节能管理', 0.704327404499054), ('贯标', 0.704214334487915), ('认定', 0.7032879590988159), ('计量', 0.7014200687408447), ('碳排放强度', 0.6967470049858093), ('梯度', 0.6937085390090942), ('目标责任', 0.6935260891914368), ('成熟度', 0.6883851289749146), ('风机', 0.6871374845504761), ('三相异步电动机', 0.6859878301620483), ('水泵', 0.6859713792800903), ('用能单位', 0.6857316493988037), ('器具', 0.6838501691818237), ('重点行业', 0.6837790608406067), ('油耗', 0.6828922033309937), ('星级', 0.6827845573425293), ('办公', 0.6823928356170654), ('变频', 0.6823465824127197), ('节能', 0.6811280250549316), ('电冰箱', 0.6785842776298523), ('普及', 0.6775370240211487), ('照明', 0.6772657632827759), ('能耗', 0.6770979762077332), ('革新', 0.6755015254020691), ('达标', 0.6751096248626709), ('空调器', 0.675058901309967), ('机动车', 0.6743203997612), ('万种', 0.673894464969635), ('钢铁企业能源管控中心', 0.6724932193756104), ('对照', 0.6686010956764221), ('风机泵', 0.6669935584068298), ('审计', 0.6662198901176453), ('合成氨烧碱', 0.665794312953949), ('管理师', 0.6648439168930054), ('台阶', 0.6648297905921936), ('通则', 0.6638041734695435), ('家用电器', 0.6636797785758972), ('认证', 0.6633787155151367), ('效果好', 0.661034882068634), ('内燃机', 0.6599903106689453), ('电机', 0.6599215269088745), ('2021', 0.6591108441352844), ('考核指标', 0.6588438749313354), ('测定', 0.6585050821304321), ('拖动', 0.6551119685173035), ('营运', 0.6550856828689575), ('单位产品能耗', 0.6536070108413696), ('惠民', 0.6530255675315857), ('热量', 0.6527032256126404), ('绢纺', 0.6524896025657654), ('2级', 0.6521963477134705), ('单位能耗', 0.6505882740020752), ('二级', 0.6503571271896362), ('调速', 0.6501703858375549), ('导则', 0.6489292979240417), ('有色石化', 0.6487516164779663), ('单位产品', 0.6486164331436157), ('定期', 0.6471723914146423), ('领跑', 0.6468905210494995), ('宣贯', 0.6468688249588013), ('比对', 0.6465035676956177), ('对比', 0.6457895040512085), ('变压器', 0.6451570391654968), ('接近', 0.6448172926902771)], [('用电', 0.8717301487922668), ('优惠', 0.8548086881637573), ('每千瓦时', 0.8455753922462463), ('取消', 0.8296091556549072), ('峰谷', 0.7940776944160461), ('电度', 0.784328818321228), ('加价', 0.7829515337944031), ('调整后', 0.7802636623382568), ('附加', 0.7790973782539368), ('单列', 0.7743366360664368), ('5分', 0.7642784118652344), ('甘肃省', 0.762155294418335), ('农网', 0.7614670395851135), ('还贷', 0.7599903345108032), ('铝业', 0.7597259283065796), ('2007年12月25', 0.7565835118293762), ('2008年7月1', 0.7563862204551697), ('钱', 0.7549694180488586), ('惩罚性', 0.7547998428344727), ('铝厂', 0.753861129283905), ('改为', 0.7499887943267822), ('合成氨电炉黄磷类', 0.7493447661399841), ('1分钱', 0.749198317527771), ('日起', 0.7469414472579956), ('5分钱', 0.7416122555732727), ('氯碱', 0.7393990159034729), ('1厘', 0.7379248738288879), ('后期', 0.7374412417411804), ('电费', 0.7369133830070496), ('铝锭类', 0.7367921471595764), ('非居民', 0.7364841103553772), ('湖南省', 0.7359055876731873), ('排灌', 0.7357560992240906), ('堆化工厂', 0.7355542182922363), ('并入', 0.7341820001602173), ('水库', 0.7335681915283203), ('合成氨电炉黄磷电', 0.733466625213623), ('通顺', 0.7325663566589355), ('铜川鑫光铝业公司', 0.7324151992797852), ('阶梯电价', 0.7310118675231934), ('黄磷类', 0.7303281426429749), ('2008年7月1日', 0.7291318774223328), ('88厘', 0.7278745770454407), ('平果', 0.726523220539093), ('电类', 0.7257615923881531), ('附加费', 0.7245007753372192), ('大工', 0.7228716015815735), ('电', 0.7172443270683289), ('17分', 0.71700119972229), ('01元', 0.7165732979774475), ('分档', 0.7161548733711243), ('02元', 0.7160953283309937), ('磷类', 0.7134842872619629), ('铁合金', 0.7129912376403809), ('公用事业', 0.7129271030426025), ('行大', 0.7115649580955505), ('4分钱', 0.7105569839477539), ('2分钱', 0.7086755633354187), ('征收', 0.7078828811645508), ('抗灾', 0.7066247463226318), ('移民', 0.7052035927772522), ('分时', 0.7044060230255127), ('优', 0.7030964493751526), ('丰水期', 0.7023369073867798), ('kWh', 0.7023367881774902), ('2009年1月1日', 0.7019628286361694), ('剩余', 0.7018910050392151), ('类别', 0.7012823820114136), ('部制', 0.7010276317596436), ('铁合', 0.7005968689918518), ('其余', 0.7001152634620667), ('上网', 0.698654055595398), ('三峡工程建设', 0.6975609660148621), ('001元', 0.6969608664512634), ('核工业', 0.6966435313224792), ('015元', 0.6953305602073669), ('收缴', 0.6937718987464905), ('缴纳', 0.692944347858429), ('用电量', 0.692700982093811), ('合并', 0.690954864025116), ('电炉', 0.6903818249702454), ('可比', 0.6880500912666321), ('枯水期', 0.6828668117523193), ('2分', 0.6825228333473206), ('64', 0.682426393032074), ('收取', 0.6822826266288757), ('所属', 0.6820412874221802), ('差别电价', 0.6803091168403625), ('黄', 0.679962158203125), ('电量', 0.6788419485092163)], [('化解', 0.962216854095459), ('脱困', 0.9195048809051514), ('要点', 0.9059509038925171), ('2018年', 0.8957573175430298), ('钢死灰复燃', 0.8826174736022949), ('去产能', 0.8776162266731262), ('2019年', 0.8626680374145508), ('地条', 0.8552912473678589), ('新阶段', 0.8489638566970825), ('复产', 0.8487523198127747), ('压减', 0.8402262926101685), ('国有资产', 0.8399588465690613), ('僵尸', 0.8388367891311646), ('坚持用', 0.8226328492164612), ('扎实', 0.8194326758384705), ('出清', 0.8103060126304626), ('法治化', 0.8086864948272705), ('国企', 0.8027082085609436), ('联席会议', 0.8011888861656189), ('死灰复燃', 0.8005250096321106), ('谋划', 0.7980289459228516), ('节奏', 0.7935642600059509), ('防范', 0.7934615015983582), ('实质性', 0.793403685092926), ('工作经验', 0.790946900844574), ('转入', 0.7906702160835266), ('破产', 0.7902810573577881), ('工作报告', 0.7871729135513306), ('稳中求进', 0.7836107611656189), ('部际', 0.7835195064544678), ('蓝天保卫战', 0.7829674482345581), ('批示', 0.7815030217170715), ('实效', 0.7813518643379211), ('艰巨性', 0.7812454700469971), ('产能过剩', 0.7786715030670166), ('解决好', 0.7775905132293701), ('转产', 0.7774912118911743), ('2018', 0.7760382890701294), ('小组', 0.7757251262664795), ('基调', 0.7756479382514954), ('各单位', 0.774368166923523), ('环境问题', 0.7733972072601318), ('坚决', 0.7733476161956787), ('阶段性', 0.7724636197090149), ('回头看', 0.7712438702583313), ('今年', 0.771034300327301), ('债务', 0.7691701054573059), ('紧迫性', 0.7677232027053833), ('201822号', 0.7654467821121216), ('重中之重', 0.7652244567871094), ('重要性', 0.7651822566986084), ('协助', 0.7642092704772949), ('首都', 0.7641624808311462), ('批转', 0.7638733983039856), ('认识', 0.7631018757820129), ('转发', 0.7628326416015625), ('高峰', 0.761869490146637), ('非常', 0.7617773413658142), ('落地', 0.7615910172462463), ('产能严重过剩', 0.760363757610321), ('严防', 0.7592320442199707), ('一项', 0.7591890096664429), ('大胆', 0.7591832280158997), ('中央经济工作会议', 0.75911545753479), ('决定', 0.7584909796714783), ('原材料工业', 0.7563306093215942), ('总责', 0.7559064030647278), ('大力宣传', 0.7556037306785583), ('坚定不移', 0.75555819272995), ('如下', 0.7551385164260864), ('供给侧结构性改革', 0.7547673583030701), ('务实', 0.7539359927177429), ('稳妥', 0.7538294792175293), ('河北省', 0.7526118159294128), ('上来', 0.7525851130485535), ('务求', 0.7520512938499451), ('施策', 0.7516619563102722), ('摸清', 0.7515096068382263), ('河北省人民政府', 0.7513651251792908), ('关切', 0.7513222694396973), ('新特点', 0.7502776980400085), ('保供', 0.7502667903900146), ('大局', 0.7498133778572083), ('两高', 0.749554455280304), ('不快', 0.748225748538971), ('抓实', 0.7475470304489136), ('推向', 0.7450082898139954), ('抓出成效', 0.7448108792304993), ('长期性', 0.7447519898414612), ('混合所有制', 0.7438868880271912)], [('淘汰', 0.9137362241744995), ('按期', 0.8394202589988708), ('淘汰落后', 0.8180179595947266), ('发达地区', 0.7657139301300049), ('退出', 0.7612131237983704), ('三年内', 0.7602515816688538), ('产业结构调整', 0.7584433555603027), ('解决好', 0.7584396600723267), ('明令', 0.7527488470077515), ('落后', 0.7499600052833557), ('关停', 0.747887372970581), ('2019', 0.742989718914032), ('拆除', 0.742297351360321), ('超额', 0.7420684099197388), ('名单', 0.7412015199661255), ('公告', 0.7399395704269409), ('近期', 0.7398922443389893), ('期限', 0.737511396408081), ('中央财政', 0.7369828224182129), ('等量', 0.7339313626289368), ('量化', 0.7313810586929321), ('时间表', 0.7276811003684998), ('20107号', 0.7264333963394165), ('界定', 0.7221278548240662), ('关闭', 0.7216798067092896), ('奖励', 0.7209382057189941), ('期限内', 0.7183518409729004), ('淘汰关停', 0.7180057168006897), ('任务分解', 0.7167492508888245), ('提前', 0.7166574597358704), ('死灰复燃', 0.7153071761131287), ('破产', 0.7148784399032593), ('谎报', 0.7140929698944092), ('时限', 0.7134804725646973), ('转产', 0.7127848267555237), ('出清', 0.7127143144607544), ('新上', 0.7112724184989929), ('财力', 0.7109149694442749), ('未完成', 0.7107426524162292), ('格式', 0.7106158137321472), ('疏导', 0.7090075612068176), ('彻底', 0.7077362537384033), ('对策', 0.706486701965332), ('7200万吨', 0.7064706087112427), ('低效', 0.7061697244644165), ('阶段性', 0.705970287322998), ('12009年', 0.7056640982627869), ('压减', 0.7054723501205444), ('指导目录', 0.7053639888763428), ('指令', 0.7053154110908508), ('区市', 0.7046065330505371), ('逐级', 0.7045866847038269), ('以前', 0.7043768763542175), ('地条', 0.703886091709137), ('到位', 0.703777551651001), ('2011年', 0.7021106481552124), ('涉及', 0.7014580368995667), ('转入', 0.6998524069786072), ('一并', 0.6996676325798035), ('省份', 0.6991754770278931), ('具体目标', 0.6975910663604736), ('倒逼', 0.6967924237251282), ('上大压小', 0.6966877579689026), ('产能严重过剩', 0.6965056657791138), ('不达标', 0.6959308981895447), ('管理暂行办法', 0.6943225264549255), ('钢死灰复燃', 0.6935214400291443), ('缓建', 0.6926968693733215), ('25亿吨', 0.6925721764564514), ('保供', 0.6924890875816345), ('限期', 0.6924794912338257), ('企业兼并', 0.6924074292182922), ('12012年', 0.6910934448242188), ('重整', 0.6910380721092224), ('分批', 0.6904205679893494), ('2009年', 0.6903993487358093), ('12000万吨', 0.68961501121521), ('样式', 0.6893244981765747), ('任务', 0.6886394619941711), ('进度', 0.6884024739265442), ('铁炼钢', 0.687772810459137), ('惩罚', 0.6870337724685669), ('本年度', 0.686704695224762), ('回头看', 0.685415506362915), ('产能过快增长', 0.6850993037223816), ('200736号', 0.6835198998451233), ('艰巨性', 0.6831724643707275), ('列入', 0.6830024719238281), ('3月', 0.6829952001571655), ('200938号', 0.680889368057251)], [('高新技术产业', 0.7203525304794312), ('新引擎', 0.6884100437164307), ('服务链', 0.6874911189079285), ('高起点', 0.6873251795768738), ('柞蚕', 0.6866645216941833), ('高端化', 0.6832221150398254), ('城市群', 0.6776768565177917), ('资源要素', 0.6757870316505432), ('支柱性', 0.6756406426429749), ('装备制造业', 0.6740095019340515), ('错位', 0.6739379167556763), ('专精特新', 0.6739307641983032), ('壮大', 0.6736849546432495), ('平原', 0.6729685068130493), ('渐趋', 0.6729353070259094), ('做优', 0.6723152995109558), ('做强', 0.6719775795936584), ('标志性', 0.6715542078018188), ('战略联盟', 0.6698430180549622), ('上下游', 0.6697432398796082), ('先导', 0.6690558791160583), ('世界级', 0.668322741985321), ('上游', 0.6673668026924133), ('质量好', 0.6663714051246643), ('初见成效', 0.6663191914558411), ('前沿性', 0.6659902334213257), ('高精尖', 0.6652174592018127), ('承接', 0.665163516998291), ('抢占', 0.6650630831718445), ('成长', 0.6649200916290283), ('增长点', 0.6646090745925903), ('雄厚', 0.6644060611724854), ('一二三', 0.6639447212219238), ('煤水', 0.6634942889213562), ('关联', 0.6633022427558899), ('集聚区', 0.6631988286972046), ('真正', 0.6619145274162292), ('独特', 0.6615379452705383), ('集聚', 0.6613633632659912), ('汽车工业', 0.6608814597129822), ('跨行业', 0.6596541404724121), ('更广', 0.6595175862312317), ('百余个', 0.6586388349533081), ('前瞻', 0.658531129360199), ('做大', 0.657840371131897), ('不可或缺', 0.6576492786407471), ('主业', 0.6574272513389587), ('新格局', 0.6571069955825806), ('新进展', 0.6569727659225464), ('构筑', 0.6555812358856201), ('科技含量', 0.655296266078949), ('蓬勃', 0.6548544764518738), ('催生', 0.6547507047653198), ('蚕丝', 0.6537477970123291), ('兴起', 0.6534655690193176), ('产业发展', 0.653224527835846), ('京津冀地区', 0.6530088186264038), ('集聚化', 0.6526517868041992), ('增长极', 0.6520963907241821), ('优化结构', 0.6520174741744995), ('聚集', 0.6518990397453308), ('东北地区', 0.6506390571594238), ('一大批', 0.649272084236145), ('产业链', 0.6491496562957764), ('示范区', 0.6489979028701782), ('切入点', 0.6487143635749817), ('密集', 0.6484879851341248), ('强大', 0.6484789848327637), ('大中小企业', 0.6482341885566711), ('正逐步', 0.6480703949928284), ('新动力', 0.647997260093689), ('为主导', 0.6477822065353394), ('效应', 0.6475529670715332), ('中西部', 0.6474441885948181), ('跨越式', 0.6472413539886475), ('驱动力', 0.6466478705406189), ('昆明', 0.6465365290641785), ('提质', 0.6461947560310364), ('向上', 0.6461656093597412), ('主攻', 0.6461272239685059), ('瞄准', 0.6455896496772766), ('相互', 0.6454154253005981), ('共同体', 0.6454000473022461), ('海峡', 0.6453286409378052), ('特色化', 0.6452946662902832), ('支柱', 0.6444768905639648), ('产业规模', 0.6436859965324402), ('开发区', 0.6436576247215271), ('后发', 0.6433916687965393), ('12家', 0.6431673765182495)], [('20162020', 0.8671774864196777), ('年现', 0.8559346199035645), ('20112015年', 0.8498027920722961), ('2016', 0.8455751538276672), ('国家粮食局', 0.8441411852836609), ('个子', 0.8431149125099182), ('指导性', 0.8251658082008362), ('20162020年', 0.821753740310669), ('粮展', 0.8204136490821838), ('原材料工业', 0.8126074075698853), ('规划期', 0.8103861212730408), ('十四五', 0.8100594878196716), ('国家质量监督检验检疫总局', 0.8053260445594788), ('编制', 0.7991899251937866), ('组成', 0.7964102029800415), ('远景', 0.7956864833831787), ('指导意见', 0.7953485250473022), ('纲要', 0.7934256792068481), ('第十二个', 0.7898250222206116), ('中长期发展规划', 0.7851424217224121), ('阐明', 0.7829746603965759), ('建筑卫生陶瓷工业', 0.7816187739372253), ('意见', 0.7809464335441589), ('非金属矿工业', 0.7797880172729492), ('十三五规划', 0.7797759175300598), ('2035年', 0.772121250629425), ('国家发展和改革委员会', 0.7713913917541504), ('201222号', 0.7693871855735779), ('2006', 0.7656395435333252), ('我局', 0.7637460231781006), ('201032号', 0.7633520364761353), ('第十一个', 0.7627383470535278), ('20122020年', 0.7625225782394409), ('战略部署', 0.7623549103736877), ('文件', 0.7589765787124634), ('第一条', 0.7574723362922668), ('遵照', 0.7542756199836731), ('细化', 0.7533377408981323), ('第十三个', 0.7521587014198303), ('调整和振兴规划', 0.7517518997192383), ('本地', 0.7502449154853821), ('节能减碳', 0.7486922740936279), ('中华人民共和国', 0.7472524046897888), ('2012', 0.7440803050994873), ('印发', 0.7413976788520813), ('依据', 0.7407359480857849), ('第十四个', 0.7407103776931763), ('简称', 0.7403835654258728), ('部住房和城乡建设部', 0.7401039600372314), ('商务部', 0.7391574382781982), ('201126号', 0.7379817366600037), ('第二批', 0.7350644469261169), ('审议', 0.7329604029655457), ('国家食品药品监督管理总局', 0.7326359152793884), ('大变强', 0.7320762276649475), ('报请', 0.7290794253349304), ('部务', 0.727618396282196), ('委员会', 0.726935863494873), ('国办', 0.7266831398010254), ('运发', 0.7238067984580994), ('指南', 0.7206485271453857), ('2020', 0.7197953462600708), ('产业规划', 0.7189119458198547), ('计划生育', 0.7183948159217834), ('中共中央', 0.7181712985038757), ('我委', 0.7166961431503296), ('2011', 0.7165115475654602), ('国家林业局', 0.7165082693099976), ('复杂性', 0.71351557970047), ('实际', 0.7120732665061951), ('工信部', 0.7109577655792236), ('住房城乡建设部', 0.7101913094520569), ('会同', 0.7097410559654236), ('征求', 0.7086980938911438), ('碳达峰', 0.7070469260215759), ('蓝天保卫战', 0.7054867148399353), ('科', 0.7048053741455078), ('规划', 0.7039471864700317), ('综合性', 0.703900933265686), ('2013', 0.7030971646308899), ('一十一五', 0.7016123533248901), ('高质量发展', 0.701109766960144), ('如下', 0.7008664608001709), ('部门分工', 0.6993017196655273), ('既是', 0.6990215182304382), ('2009', 0.6989791989326477), ('抓紧', 0.6971408724784851), ('区划', 0.6968831419944763), ('低碳产业', 0.6962683796882629), ('对应', 0.6962124705314636)], [('先导', 0.8856930732727051), ('产学研', 0.8761895895004272), ('技术创新', 0.8653253316879272), ('标志性', 0.8613625168800354), ('各方面', 0.8592132329940796), ('大众创业万众', 0.8530085682868958), ('驱动力', 0.8479099273681641), ('团队', 0.8441067337989807), ('战略联盟', 0.843655526638031), ('策源地', 0.8409453630447388), ('前沿性', 0.8374910950660706), ('前瞻性', 0.8348019123077393), ('高地', 0.8346657156944275), ('汇聚', 0.8332564234733582), ('大专院校', 0.8331085443496704), ('产学研用', 0.832626461982727), ('跨越', 0.8324815630912781), ('力量', 0.8309205174446106), ('突破口', 0.8295518755912781), ('学科', 0.8263450860977173), ('带头', 0.8259320855140686), ('高校', 0.8245087265968323), ('塑造', 0.8240009546279907), ('特色化', 0.8233224749565125), ('策源', 0.8226407170295715), ('资源要素', 0.8208394646644592), ('基础性', 0.8198402523994446), ('吸引', 0.8198211193084717), ('工业设计中心', 0.8180364370346069), ('大学', 0.8176051378250122), ('人才培养', 0.8170180320739746), ('新局面', 0.8160058856010437), ('引领', 0.8158865571022034), ('成长', 0.8133206367492676), ('两用', 0.812836766242981), ('孵化器', 0.8124573826789856), ('带动作用', 0.8116811513900757), ('中华', 0.8112612366676331), ('兴起', 0.8110384941101074), ('三品', 0.8106266260147095), ('契机', 0.8104186654090881), ('思维', 0.80990070104599), ('科技进步', 0.8093895316123962), ('科技成果', 0.8081551194190979), ('紧跟', 0.807492733001709), ('双创', 0.8074004054069519), ('科技', 0.8069655299186707), ('示范区', 0.8051451444625854), ('跨界', 0.8051095604896545), ('内生动力', 0.8049070239067078), ('国家工程', 0.8043619990348816), ('揭榜', 0.8041453957557678), ('原始', 0.8038327693939209), ('着眼于', 0.8035127520561218), ('为主体', 0.803320586681366), ('智力', 0.8033071756362915), ('缺少', 0.8031256198883057), ('协同效应', 0.8030762076377869), ('新动力', 0.8026269674301147), ('院所', 0.7997768521308899), ('支撑', 0.7995812892913818), ('创客', 0.7993853688240051), ('科研', 0.7993075251579285), ('对外开放', 0.7991354465484619), ('视野', 0.7985172867774963), ('多学科', 0.7981067299842834), ('众创空间', 0.7977646589279175), ('瞄准', 0.7977457642555237), ('国家级', 0.7966994047164917), ('中心国家重点实验室', 0.7964282631874084), ('联盟', 0.7963185906410217), ('美丽', 0.795811116695404), ('主攻', 0.7955778241157532), ('大批', 0.7952037453651428), ('企业家', 0.7943782806396484), ('高新技术产业', 0.7939982414245605), ('束缚', 0.7939079403877258), ('投入力度', 0.7935858368873596), ('主导', 0.7924481630325317), ('薄弱环节', 0.7909777760505676), ('高水平', 0.7907838821411133), ('为主导', 0.7907516956329346), ('高素质', 0.7906903028488159), ('催生', 0.7906327843666077), ('实训', 0.7902379035949707), ('一大批', 0.7885773777961731), ('助力', 0.7880530953407288), ('软实力', 0.7878822684288025), ('中央财政科技计划专项', 0.7861262559890747), ('主线', 0.7860393524169922)], [('产学研', 0.9063147306442261), ('产学研用', 0.8702078461647034), ('科技创新', 0.8653253316879272), ('为主体', 0.8582327961921692), ('战略联盟', 0.8456823229789734), ('联盟', 0.833819568157196), ('研发投入', 0.8309304714202881), ('共性', 0.8030650615692139), ('驱动力', 0.8014342188835144), ('标志性', 0.8002980351448059), ('基础性', 0.7927594184875488), ('院所', 0.7909831404685974), ('科研', 0.7904012799263), ('创新能力', 0.7882132530212402), ('前沿性', 0.7856570482254028), ('高校', 0.7847795486450195), ('前瞻性', 0.781190812587738), ('大专院校', 0.7771879434585571), ('投入力度', 0.7748788595199585), ('国家工程', 0.7743921875953674), ('相结合', 0.7709699273109436), ('先导', 0.7705425024032593), ('科技成果', 0.7681387066841125), ('原始', 0.7673014998435974), ('中央财政科技计划专项', 0.7652230262756348), ('高地', 0.7636276483535767), ('攻关', 0.7615633010864258), ('紧跟', 0.7568905353546143), ('力量', 0.7563571333885193), ('两化融合', 0.7553977370262146), ('跨行业', 0.7553856372833252), ('大学', 0.7540594935417175), ('薄弱环节', 0.752502977848053), ('生物医学', 0.7524007558822632), ('工程化', 0.7520131468772888), ('引领', 0.7513996958732605), ('家蚕', 0.7508753538131714), ('主导', 0.7482336759567261), ('投入', 0.7476023435592651), ('缺少', 0.7464755773544312), ('两用', 0.7460010051727295), ('内生动力', 0.7459393739700317), ('前沿', 0.745745837688446), ('中心国家重点实验室', 0.7453811168670654), ('转让', 0.7451456189155579), ('一大批', 0.7444573640823364), ('技术研发', 0.7439376711845398), ('科技', 0.7431913018226624), ('孵化器', 0.7431498765945435), ('多学科', 0.7403802871704102), ('双创', 0.7403386831283569), ('催生', 0.7399945259094238), ('核心技术', 0.7397751212120056), ('国际领先', 0.7397102117538452), ('理论', 0.7388415932655334), ('策源地', 0.7386484742164612), ('跨越', 0.738623857498169), ('带头', 0.7378246188163757), ('跨界', 0.7374668717384338), ('整车', 0.7366462349891663), ('激励机制', 0.7366406917572021), ('纵向', 0.7357364296913147), ('人才培养', 0.7356085777282715), ('汇聚', 0.7354758381843567), ('生命线', 0.7346580028533936), ('资源要素', 0.7336049675941467), ('颠覆性', 0.7334911823272705), ('支撑', 0.733441174030304), ('科技进步', 0.7332794666290283), ('紧密', 0.732866644859314), ('团队', 0.7328451871871948), ('实验', 0.7325686812400818), ('新动力', 0.732216477394104), ('小巨人', 0.7317928671836853), ('软实力', 0.7311630249023438), ('策源', 0.7311504483222961), ('揭榜', 0.729714035987854), ('九大', 0.729611873626709), ('大众创业万众', 0.7288712859153748), ('为主导', 0.7277483344078064), ('瞄准', 0.7268324494361877), ('学研', 0.7258796691894531), ('工业设计中心', 0.7257665395736694), ('带动作用', 0.7255545258522034), ('百余个', 0.7248396277427673), ('牵引', 0.7237155437469482), ('难题', 0.7236586809158325), ('突破口', 0.7231974601745605), ('真正', 0.7231877446174622), ('更广', 0.722467303276062)], [('技术研发', 0.782855749130249), ('攻关', 0.7697046995162964), ('工程化', 0.7610072493553162), ('编辑', 0.754368782043457), ('理论', 0.7534596920013428), ('生物医学', 0.7512258887290955), ('基因组', 0.7501780390739441), ('模块化', 0.7482454180717468), ('集成能力', 0.7468217611312866), ('攻克', 0.7455970048904419), ('信息库', 0.7439948916435242), ('关键技术', 0.7388765811920166), ('集成化', 0.7373940944671631), ('正负', 0.7367445826530457), ('特异性', 0.735821008682251), ('技术产业化', 0.7331971526145935), ('家蚕', 0.7320360541343689), ('工装', 0.7317360043525696), ('填补', 0.7297674417495728), ('装备自主', 0.7294163107872009), ('本地化', 0.7287745475769043), ('纳米材料', 0.7284805178642273), ('微创', 0.7273518443107605), ('纺纱', 0.7272325754165649), ('极地', 0.7267880439758301), ('适航', 0.7259351015090942), ('难题', 0.7240333557128906), ('战略联盟', 0.7235274910926819), ('共性', 0.7233692407608032), ('一条龙', 0.7233355045318604), ('集成', 0.7230930328369141), ('盾构机', 0.7195780277252197), ('软硬件', 0.7194772958755493), ('组合', 0.7187411189079285), ('分子', 0.7187086939811707), ('生物样本库', 0.7180036306381226), ('版权', 0.7172712683677673), ('存储器', 0.7164881229400635), ('首台套', 0.7164691090583801), ('二极管', 0.7161204218864441), ('缫丝机', 0.7160136699676514), ('继承', 0.7159399390220642), ('国际领先', 0.7158260345458984), ('系统化', 0.7156137824058533), ('空白', 0.7153925895690918), ('突破性', 0.7143853902816772), ('三维3D', 0.714250385761261), ('人机', 0.712347149848938), ('纺机', 0.7119138836860657), ('总装', 0.7117197513580322), ('颠覆性', 0.7115253210067749), ('信息处理', 0.7105712890625), ('针织', 0.7098223567008972), ('服务器', 0.7097719311714172), ('验证', 0.7095188498497009), ('工程菌', 0.7084653377532959), ('模仿', 0.7084289789199829), ('元器件', 0.7079534530639648), ('机电产品', 0.7074348330497742), ('电解液', 0.7073363661766052), ('表达', 0.70675128698349), ('量子', 0.7065155506134033), ('实验', 0.7057167291641235), ('机载', 0.7053396701812744), ('役再', 0.7044723629951477), ('改型', 0.7043718099594116), ('临床诊断', 0.7032977342605591), ('介入', 0.7031049132347107), ('体外诊断', 0.7029730677604675), ('生物制品', 0.7019668221473694), ('RFID', 0.7017960548400879), ('追踪', 0.7015297412872314), ('前沿', 0.7010642886161804), ('5-10个', 0.7009035348892212), ('生物反应器', 0.7006440758705139), ('机理', 0.7004544138908386), ('建设石化', 0.7002732157707214), ('涤纶', 0.7000718712806702), ('恶性肿瘤', 0.6999282240867615), ('纯化', 0.6997060179710388), ('总包', 0.6995974779129028), ('试剂', 0.6994815468788147), ('空管', 0.6993281245231628), ('多学科', 0.6991520524024963), ('选育', 0.69908607006073), ('光电子', 0.6988359093666077), ('芯片', 0.6982428431510925), ('工作站', 0.6981521844863892), ('零配件', 0.6981436014175415), ('主力', 0.6979125738143921)], [('约谈', 0.9587465524673462), ('批评', 0.9574886560440063), ('负起', 0.9457277655601501), ('处罚', 0.9454392790794373), ('依纪', 0.9419999718666077), ('撤销', 0.9404680132865906), ('死灰复燃', 0.939102053642273), ('逐一', 0.9384206533432007), ('督查', 0.9361920952796936), ('惩处', 0.9347935318946838), ('情节', 0.9337731599807739), ('通报', 0.9323328137397766), ('虚报', 0.9309535622596741), ('注销', 0.927793562412262), ('地条钢', 0.9277318120002747), ('必要时', 0.9269559383392334), ('目标责任评价', 0.9259827733039856), ('复产', 0.9258251786231995), ('不落实', 0.9251423478126526), ('停工', 0.9250457286834717), ('自查', 0.9249098300933838), ('国土资源部', 0.9244986772537231), ('核实', 0.9233874082565308), ('核减', 0.9205589890480042), ('停建', 0.9200462102890015), ('依照', 0.9200219511985779), ('责令', 0.9192910194396973), ('确认', 0.9192017316818237), ('期限', 0.918839156627655), ('督察', 0.9179970622062683), ('政纪', 0.9178527593612671), ('犯罪', 0.9177107810974121), ('批复', 0.9175319671630859), ('弄虚作假', 0.9172559976577759), ('中办', 0.9166038036346436), ('回头看', 0.916358470916748), ('排查', 0.9162188172340393), ('廊坊市', 0.9158397912979126), ('项目清理', 0.9158218502998352), ('省政府', 0.9155913591384888), ('瞒报', 0.915533721446991), ('善后', 0.9149527549743652), ('严谨', 0.9149311184883118), ('后果', 0.9146721959114075), ('200925号', 0.9133704304695129), ('谁负责', 0.91267991065979), ('落到', 0.9126741290092468), ('批转', 0.9126623868942261), ('环境保护部', 0.9122299551963806), ('谎报', 0.9121063947677612), ('营业执照', 0.9115617871284485), ('撤回', 0.9112457036972046), ('冒领', 0.9110967516899109), ('到位', 0.9108708500862122), ('整改', 0.910573422908783), ('签订', 0.909990668296814), ('复查', 0.9092745184898376), ('情形', 0.9079445600509644), ('目标责任制', 0.9079362154006958), ('省委', 0.907515287399292), ('发改办', 0.9068408608436584), ('限期', 0.9061658382415771), ('接受', 0.9060704708099365), ('抽查', 0.9057610630989075), ('党政', 0.9056546092033386), ('失职', 0.9054020047187805), ('下列', 0.9050347805023193), ('处分', 0.9050160646438599), ('摸清', 0.9044556021690369), ('行政区域内', 0.9038420915603638), ('区市', 0.9036288857460022), ('擅自', 0.9034807682037354), ('缓建', 0.9029296636581421), ('表彰', 0.90218585729599), ('总责', 0.9019642472267151), ('挪用', 0.9019282460212708), ('书面', 0.9015973210334778), ('期限内', 0.9015938639640808), ('2018', 0.9014453887939453), ('立即', 0.9014372825622559), ('实处', 0.9011243581771851), ('出具', 0.9000065326690674), ('严肃', 0.8995704054832458), ('予以', 0.8992044925689697), ('试行', 0.8981069326400757), ('落地', 0.8977218866348267), ('批示', 0.8969476819038391), ('发现', 0.8968064188957214), ('钢死灰复燃', 0.8966306447982788), ('200736号', 0.8964617252349854)], [('不符合', 0.8812278509140015), ('严把', 0.8585571050643921), ('严禁', 0.8565288782119751), ('限制', 0.8504046201705933), ('扩建', 0.8503726124763489), ('确有', 0.8456006646156311), ('扩容', 0.8372703790664673), ('环评', 0.8342831134796143), ('不达标', 0.8286147713661194), ('名义', 0.8261950612068176), ('环境影响评价', 0.8218477964401245), ('从严', 0.8208982944488525), ('新上', 0.8206131458282471), ('停止', 0.8195027709007263), ('单纯', 0.8157894611358643), ('擅自', 0.8141121864318848), ('等量', 0.8140785694122314), ('避开', 0.814027726650238), ('饮用水', 0.8135064840316772), ('新建项目', 0.8125166296958923), ('其他地区', 0.8120145797729492), ('明令', 0.8113672733306885), ('剧毒', 0.809259831905365), ('缺水', 0.8086791634559631), ('扩能', 0.8086192011833191), ('自行', 0.802096962928772), ('严控', 0.8009508848190308), ('元素氯漂白工艺', 0.8007190823554993), ('风景名胜区', 0.7990745306015015), ('界定', 0.7969021797180176), ('依规', 0.7967626452445984), ('不准', 0.7963817715644836), ('低水平', 0.7956558465957642), ('关闭', 0.7951642274856567), ('把好', 0.7948203682899475), ('立项', 0.7943997383117676), ('暂停', 0.7923577427864075), ('限期', 0.7921878099441528), ('重叠', 0.7920562028884888), ('受理', 0.7910258173942566), ('越权', 0.7874312400817871), ('高污染', 0.7873114347457886), ('审批', 0.7866959571838379), ('列入', 0.7864646911621094), ('船台', 0.786288321018219), ('边批边建', 0.7852263450622559), ('撤回', 0.7834153175354004), ('未经', 0.7833022475242615), ('位于', 0.783175528049469), ('取缔', 0.7822975516319275), ('属于', 0.7818983197212219), ('船坞', 0.7814602851867676), ('管理部门', 0.7810546159744263), ('自用', 0.7794356942176819), ('清理', 0.7776198983192444), ('期限', 0.7773404121398926), ('未批先建', 0.7766730785369873), ('新建', 0.7764752507209778), ('露天', 0.7761237025260925), ('备案', 0.7759881615638733), ('不再', 0.775754988193512), ('许可', 0.7757514715194702), ('整顿', 0.7756249904632568), ('核准', 0.7739406228065491), ('三年内', 0.7726061940193176), ('关停', 0.7714467644691467), ('过多', 0.7711637020111084), ('水源', 0.77057284116745), ('瓦斯', 0.7695701122283936), ('饱和', 0.7694372534751892), ('用地', 0.7692158818244934), ('暂缓', 0.7688905000686646), ('不得以', 0.7674041390419006), ('未达标', 0.7672168016433716), ('闲置', 0.7669613361358643), ('指令', 0.7666268944740295), ('一律', 0.76619952917099), ('下放', 0.766154408454895), ('禁限', 0.7653411626815796), ('异地', 0.7645129561424255), ('新布点', 0.764460563659668), ('彻底', 0.76378333568573), ('损失', 0.762474000453949), ('许可证', 0.7612864971160889), ('弄虚作假', 0.7611514329910278), ('保护区', 0.7607266902923584), ('远离', 0.7606280446052551), ('必要', 0.7579881548881531), ('排污', 0.7563331127166748), ('小规模', 0.7550097703933716)], [('一级', 0.9477499127388), ('万家企业', 0.9457105994224548), ('目标责任制', 0.9441409707069397), ('目标责任评价', 0.9416155815124512), ('逐级', 0.9312534332275391), ('所辖', 0.9301296472549438), ('评价考核制度', 0.92966628074646), ('目标分解', 0.9285574555397034), ('汇总', 0.9166061878204346), ('下达', 0.916552722454071), ('自查', 0.916191041469574), ('督查', 0.9135310649871826), ('地市', 0.9105173349380493), ('书面', 0.90838223695755), ('节能低碳', 0.9081869125366211), ('市县', 0.9081587195396423), ('二氧化', 0.9073730111122131), ('对照', 0.9051182270050049), ('下发', 0.9049542546272278), ('格式', 0.9049373269081116), ('行政区', 0.9047823548316956), ('现予', 0.9042974710464478), ('抄送', 0.9040607213973999), ('200736号', 0.9000546336174011), ('期末', 0.8972892165184021), ('样式', 0.8972794413566589), ('通则', 0.8965520858764648), ('结束', 0.8962545394897461), ('领导干部', 0.8957094550132751), ('狠抓', 0.8930925130844116), ('月底', 0.8926633596420288), ('督办', 0.892204761505127), ('行政区域内', 0.891647219657898), ('考评', 0.891261637210846), ('奖惩', 0.8912412524223328), ('按时', 0.89105224609375), ('国家机关', 0.8905977606773376), ('说明', 0.8873597383499146), ('评估期', 0.8873385787010193), ('任务分解', 0.8871588706970215), ('委', 0.8868703842163086), ('打分', 0.8863542079925537), ('惩罚', 0.8858447074890137), ('消纳量', 0.8855593800544739), ('电子版', 0.8849777579307556), ('目标责任考核', 0.8849771022796631), ('表彰', 0.8849725127220154), ('细则', 0.8848866820335388), ('时限', 0.8845195770263672), ('督察', 0.88448166847229), ('公布', 0.8844044804573059), ('试行', 0.8842311501502991), ('瞒报', 0.8832460045814514), ('2017年', 0.8826114535331726), ('下旬', 0.8823104500770569), ('第二条', 0.8822631239891052), ('召开', 0.8818573951721191), ('可行', 0.8801074624061584), ('班子', 0.8800187110900879), ('核查表', 0.879082202911377), ('分数', 0.8790178298950195), ('各市', 0.8785344958305359), ('上报', 0.8776886463165283), ('督促', 0.8770734071731567), ('验收', 0.8768816590309143), ('自查表', 0.8767774701118469), ('入海', 0.8762131929397583), ('表', 0.8758509755134583), ('准备', 0.8752197623252869), ('总责', 0.8751168251037598), ('国资委', 0.8747777342796326), ('第八条', 0.8739989995956421), ('派出', 0.8738895654678345), ('调研', 0.8736764788627625), ('委将', 0.8734604120254517), ('第四条', 0.8733448386192322), ('中期', 0.8731616735458374), ('了解', 0.872883677482605), ('出具', 0.8723838329315186), ('均为', 0.870972216129303), ('告知', 0.8704724907875061), ('季度', 0.8703086972236633), ('详细', 0.8694544434547424), ('本次', 0.8689077496528625), ('负责人', 0.8681508898735046), ('不定期', 0.867843508720398), ('日常', 0.8677471876144409), ('落到', 0.867633581161499), ('审计', 0.8675157427787781), ('一季度', 0.867388904094696)], [('等量', 0.8733898997306824), ('严禁', 0.8553112149238586), ('减量', 0.8426734805107117), ('上大压小', 0.8273078799247742), ('严把', 0.8270830512046814), ('低效', 0.8197720646858215), ('严控', 0.8184458613395691), ('压减', 0.8161318898200989), ('归集', 0.8154526948928833), ('有保有压', 0.8095536828041077), ('饱和', 0.8003219962120056), ('存量', 0.8002886176109314), ('弄虚作假', 0.7985854148864746), ('压缩', 0.7981064915657043), ('新上', 0.7977806925773621), ('环评', 0.7962827086448669), ('从严', 0.7957454323768616), ('地条', 0.7940933704376221), ('扩能', 0.793155312538147), ('确有', 0.792009174823761), ('依规', 0.7895120978355408), ('严防', 0.7872524261474609), ('闲置', 0.7870926260948181), ('省份', 0.7829526662826538), ('公示', 0.7814513444900513), ('发改办', 0.7813253402709961), ('名义', 0.780985414981842), ('2018', 0.7763545513153076), ('转入', 0.7762845158576965), ('钢死灰复燃', 0.7755125761032104), ('单纯', 0.7741860747337341), ('签订', 0.7702259421348572), ('焦化', 0.7699402570724487), ('环境影响评价', 0.7693433165550232), ('省内', 0.7674030661582947), ('疏导', 0.7655269503593445), ('立项', 0.764880895614624), ('死灰复燃', 0.7648144364356995), ('回头看', 0.7636194825172424), ('所有', 0.7635759711265564), ('运力', 0.7626123428344727), ('其他地区', 0.7618018388748169), ('复产', 0.7606595158576965), ('法治化', 0.7595530152320862), ('暂缓', 0.7582847476005554), ('瓦斯', 0.7573464512825012), ('船台', 0.7566788792610168), ('地条钢', 0.7560948133468628), ('报建', 0.7553772330284119), ('出清', 0.7552933692932129), ('把好', 0.754869282245636), ('坚持用', 0.7544821500778198), ('重整', 0.7511040568351746), ('扩容', 0.7508625984191895), ('越权', 0.7490658164024353), ('法制化', 0.7484194040298462), ('列为', 0.7482621669769287), ('整顿', 0.7472948431968689), ('自用', 0.7471465468406677), ('异地', 0.7462824583053589), ('破产', 0.7456101179122925), ('遏制', 0.7454894185066223), ('备案', 0.7446449398994446), ('产能', 0.743464469909668), ('清理', 0.7434072494506836), ('新布点', 0.7417234778404236), ('受理', 0.741478681564331), ('红色', 0.7414018511772156), ('新建项目', 0.7403161525726318), ('时间', 0.7400944232940674), ('退出', 0.7386916279792786), ('低水平', 0.7380374073982239), ('盲目', 0.737072765827179), ('擅自', 0.7366182804107666), ('联营', 0.736334502696991), ('举报', 0.7356462478637695), ('暂停', 0.7354925870895386), ('不得以', 0.7354258894920349), ('国有资产', 0.7354077696800232), ('自行', 0.7335755825042725), ('摸清', 0.7327628135681152), ('普查', 0.732593297958374), ('高发', 0.7323629856109619), ('边批边建', 0.7322316765785217), ('未批先建', 0.730326235294342), ('审查', 0.7301159501075745), ('时序', 0.7278751134872437), ('总量控制', 0.7276853919029236), ('在建', 0.7274702191352844), ('2017', 0.7264878749847412)], [('制修订', 0.8215507864952087), ('修订', 0.7690765857696533), ('规程', 0.7294842600822449), ('强制性', 0.7253645658493042), ('公约', 0.7107388377189636), ('接轨', 0.7072673439979553), ('认证', 0.7053064703941345), ('宣贯', 0.6993516087532043), ('GHS', 0.6926347613334656), ('限额', 0.6844512224197388), ('1000项', 0.6837016940116882), ('制订', 0.6835029125213623), ('对比', 0.6817365288734436), ('产品质量', 0.6814168691635132), ('质量保证', 0.6795927286148071), ('GMP', 0.6751362085342407), ('团体', 0.6749992966651917), ('规章', 0.6740675568580627), ('认可', 0.6671294569969177), ('规范', 0.6643159985542297), ('法规', 0.6605570912361145), ('方法', 0.6602088809013367), ('对标', 0.6577923893928528), ('碳足迹', 0.6568746566772461), ('比对', 0.6537467837333679), ('互认', 0.6519296169281006), ('颁布', 0.6482448577880859), ('有效性', 0.6458571553230286), ('测定', 0.6432862281799316), ('十三', 0.6428170800209045), ('声明', 0.6381258368492126), ('EHS', 0.6377089619636536), ('重点用能单位节能管理', 0.6347358226776123), ('修理', 0.6344936490058899), ('必备条件', 0.6332122683525085), ('检疫', 0.6327214241027832), ('门槛', 0.6319614052772522), ('加工业急需制', 0.6299150586128235), ('疗效', 0.629727303981781), ('自我', 0.6280152201652527), ('术语', 0.6277428865432739), ('修改', 0.6275014281272888), ('标签', 0.6271701455116272), ('矿权', 0.6269876956939697), ('通用', 0.6263486742973328), ('一致性', 0.6262305974960327), ('领跑者', 0.6256266236305237), ('编码', 0.6249391436576843), ('缺失', 0.6247456669807434), ('操作规程', 0.6245882511138916), ('标识', 0.6245099306106567), ('军用', 0.6223593950271606), ('适用性', 0.6221837401390076), ('法律', 0.621849775314331), ('测试', 0.6214450001716614), ('质量控制', 0.6213462948799133), ('加紧', 0.6209878921508789), ('贯标', 0.6201308369636536), ('质量检测', 0.6195592284202576), ('基准', 0.6192600131034851), ('级别', 0.6190055012702942), ('排放标准', 0.6187834143638611), ('体系制', 0.618433952331543), ('认可度', 0.6175472736358643), ('职业', 0.6172013282775879), ('联合国', 0.6155565977096558), ('出品率', 0.6147231459617615), ('活动组织行业协会', 0.614578366279602), ('双向', 0.613366425037384), ('涵盖', 0.6120597720146179), ('采信', 0.6114060878753662), ('机电设备', 0.6107632517814636), ('工序', 0.6101815700531006), ('标杆', 0.6101340651512146), ('规章制度', 0.6097924113273621), ('规则', 0.6085472702980042), ('溯源', 0.6076915860176086), ('鉴别', 0.6070811748504639), ('均一性', 0.6059595942497253), ('清洁生产促进法', 0.6054325103759766), ('质量检验', 0.6051086783409119), ('参与', 0.6045283079147339), ('不健全', 0.6040434837341309), ('单耗', 0.6031424403190613), ('准则', 0.6013636589050293), ('权威', 0.6004649996757507), ('样品', 0.5995270609855652), ('话语权', 0.5991446375846863), ('检测', 0.5990145206451416), ('产业政策', 0.5986734628677368)], [('出清', 0.9059851765632629), ('转入', 0.9052910804748535), ('上大压小', 0.8950458765029907), ('破产', 0.8846931457519531), ('转产', 0.8760051131248474), ('钢死灰复燃', 0.8576204180717468), ('省份', 0.8569127321243286), ('地条', 0.8479401469230652), ('僵尸', 0.8467605113983154), ('企业兼并', 0.8459770679473877), ('首钢', 0.8421217203140259), ('过剩产能', 0.8402262926101685), ('2017', 0.8395415544509888), ('宝钢', 0.8368772864341736), ('脱困', 0.8360491991043091), ('杭钢', 0.8350304365158081), ('产能严重过剩', 0.832590639591217), ('严禁', 0.8306317925453186), ('淘汰关停', 0.8281545639038086), ('解决好', 0.8279668688774109), ('要点', 0.825421929359436), ('退出', 0.8250470161437988), ('立足于', 0.8248528838157654), ('按期', 0.8232952952384949), ('死灰复燃', 0.8228123188018799), ('青钢', 0.8216249942779541), ('近期', 0.8212248682975769), ('回头看', 0.8209481835365295), ('运力', 0.8198803663253784), ('联营', 0.8182848691940308), ('15亿吨', 0.8182494044303894), ('上述', 0.81697016954422), ('置换', 0.8161319494247437), ('饱和', 0.8158491849899292), ('经济圈', 0.815609335899353), ('2019', 0.8150853514671326), ('红色', 0.8144648671150208), ('所在', 0.8136945366859436), ('实质性', 0.8134077787399292), ('摸清', 0.8133711218833923), ('京津冀', 0.8118886947631836), ('严防', 0.811736524105072), ('坚持用', 0.8107616305351257), ('武钢', 0.8103976845741272), ('资不抵债', 0.8103830814361572), ('复产', 0.8102222084999084), ('25亿吨', 0.809819221496582), ('提前', 0.8094902634620667), ('联合重组', 0.808685302734375), ('时间', 0.8083796501159668), ('散乱', 0.8081835508346558), ('省内', 0.8080228567123413), ('在建', 0.8077062964439392), ('环境问题', 0.8064484596252441), ('列为', 0.8063676357269287), ('2018', 0.8047513365745544), ('去产能', 0.8043617010116577), ('抓出成效', 0.8024126291275024), ('首都', 0.8019647598266602), ('抓实', 0.8018064498901367), ('2018年', 0.801694929599762), ('发达地区', 0.8015263676643372), ('有保有压', 0.801363468170166), ('阶段性', 0.8010064959526062), ('保供', 0.8006591796875), ('先后', 0.8006302118301392), ('国有资产', 0.8004745841026306), ('2亿吨', 0.8000912666320801), ('重整', 0.7998358011245728), ('三年内', 0.7990843653678894), ('疏导', 0.7987281680107117), ('清查', 0.7987142205238342), ('2019年', 0.7977048754692078), ('节奏', 0.7974053621292114), ('等量', 0.7969318628311157), ('立即', 0.7965208292007446), ('低效', 0.7965008616447449), ('钢厂', 0.796461284160614), ('弄虚作假', 0.7963324785232544), ('攀钢', 0.7962018847465515), ('新阶段', 0.7961381077766418), ('长三角地区', 0.7957605719566345), ('以前', 0.795657217502594), ('产能过快增长', 0.7955821752548218), ('河北省', 0.7952757477760315), ('核定', 0.7951008081436157), ('谋划', 0.795045018196106), ('连续性', 0.7950379848480225), ('暂缓', 0.7949519753456116), ('生存能力', 0.7949106693267822)], [('经济政策', 0.829470694065094), ('全额', 0.8209713101387024), ('后期', 0.811764657497406), ('消费税', 0.8091437816619873), ('峰谷', 0.8011630177497864), ('政府采购', 0.7990415692329407), ('税收', 0.7973538041114807), ('定价', 0.7956384420394897), ('所得税', 0.7945590019226074), ('补助', 0.793581485748291), ('竞价', 0.7907003164291382), ('共担', 0.7875232100486755), ('折旧', 0.7869754433631897), ('出台', 0.7819079160690308), ('财政', 0.7817835211753845), ('厂丝', 0.7798777222633362), ('财税', 0.7771840691566467), ('贸易救济', 0.7770413160324097), ('放宽', 0.7769109606742859), ('减免', 0.7767069935798645), ('援助', 0.7762814164161682), ('产权', 0.7759585976600647), ('水库', 0.7755568623542786), ('特许经营权', 0.7753668427467346), ('招标', 0.7742846608161926), ('杠杆', 0.7735406756401062), ('同等', 0.7735257148742676), ('额度', 0.7734952569007874), ('财政投入', 0.7732968926429749), ('集体', 0.7727567553520203), ('林权', 0.7725310325622559), ('科研项目', 0.7724266648292542), ('附加', 0.7718685865402222), ('支农', 0.7717610597610474), ('税费', 0.771644115447998), ('设定', 0.7706127762794495), ('预算', 0.770339846611023), ('移民', 0.7699573636054993), ('费率', 0.7699221968650818), ('保障性', 0.7696205377578735), ('投向', 0.7687596678733826), ('享受', 0.7681595087051392), ('优惠政策', 0.7674890756607056), ('地方政府', 0.7666696310043335), ('激励', 0.7656840682029724), ('资金', 0.7648639678955078), ('医保', 0.76375812292099), ('金融政策', 0.7620598077774048), ('初始', 0.7616758942604065), ('筹集', 0.759059488773346), ('货币', 0.7571218013763428), ('增值税', 0.7561770677566528), ('商业性', 0.7521148920059204), ('贴息', 0.7521021366119385), ('收益', 0.7514917254447937), ('外汇', 0.7514725923538208), ('88厘', 0.7502182126045227), ('流动', 0.7498634457588196), ('投融资体制', 0.7497484683990479), ('民营企业', 0.7494958639144897), ('反倾销', 0.7488502860069275), ('社会保障', 0.7486293911933899), ('帮扶', 0.7481229305267334), ('挂钩', 0.7468267679214478), ('购买', 0.746155858039856), ('贫困户', 0.745938777923584), ('储备棉', 0.745912492275238), ('出入境', 0.7454220652580261), ('建筑师', 0.745114266872406), ('非资源型', 0.7451075315475464), ('公益性岗位', 0.7448088526725769), ('支持性', 0.7446713447570801), ('有保有控', 0.7443777918815613), ('再贴现', 0.7437288165092468), ('债权', 0.7429351806640625), ('收储', 0.7427656054496765), ('富余', 0.7415880560874939), ('货币政策', 0.7415805459022522), ('财税政策', 0.7410331964492798), ('便利化', 0.7406334280967712), ('转岗', 0.740551233291626), ('撬动', 0.7402292490005493), ('出资', 0.7389582991600037), ('支付', 0.7386476397514343), ('个人', 0.7381330728530884), ('权利', 0.737067699432373), ('缓缴', 0.7369471192359924), ('资源性', 0.7367239594459534), ('期货', 0.7363449931144714), ('协议', 0.7362866401672363)], [('金融政策', 0.9135354161262512), ('税收', 0.9000727534294128), ('经济政策', 0.8978493213653564), ('优惠政策', 0.8971198201179504), ('减免', 0.8787180781364441), ('财税政策', 0.8783465027809143), ('外汇', 0.86684250831604), ('有保有压', 0.8570430278778076), ('消费税', 0.8550150990486145), ('倾斜', 0.8542478680610657), ('放宽', 0.8522840142250061), ('多渠道', 0.8465462923049927), ('风险补偿', 0.8448513746261597), ('落实好', 0.8424000144004822), ('所得税', 0.8420692682266235), ('共担', 0.834041953086853), ('出入境', 0.8339338302612305), ('分担', 0.8328937292098999), ('贴息', 0.8290714621543884), ('债权', 0.828112006187439), ('流动', 0.8266274333000183), ('支农', 0.8253428339958191), ('激励', 0.8252754211425781), ('税费', 0.8249716758728027), ('资源税', 0.8246824145317078), ('差别电价', 0.8245300054550171), ('厂丝', 0.8240799307823181), ('允许', 0.8233931660652161), ('再贴现', 0.821982741355896), ('货币', 0.8216426372528076), ('折旧', 0.8208854794502258), ('贸易救济', 0.8203776478767395), ('发债', 0.8194762468338013), ('激励机制', 0.8191192150115967), ('杠杆', 0.8179520964622498), ('补助', 0.8176799416542053), ('投向', 0.8171176910400391), ('预算', 0.8169121146202087), ('商业性', 0.8160349726676941), ('财政投入', 0.8149433135986328), ('增值税', 0.8138373494148254), ('林权', 0.8137202858924866), ('援助', 0.8134138584136963), ('出台', 0.8127196431159973), ('资源性', 0.8113501071929932), ('同等', 0.8109963536262512), ('操作性', 0.8109520077705383), ('海关', 0.8097677826881409), ('有保有控', 0.8095293641090393), ('区别对待', 0.8092963695526123), ('保值', 0.8089543581008911), ('特许经营权', 0.8077295422554016), ('环境监管', 0.8070189356803894), ('公益性岗位', 0.805582582950592), ('享受', 0.8051677942276001), ('政策性', 0.803832471370697), ('公益性', 0.8037883043289185), ('土地', 0.8033356666564941), ('十四', 0.8026493787765503), ('收储', 0.8025644421577454), ('财政', 0.80184006690979), ('政策', 0.8016077876091003), ('三强化', 0.8010576367378235), ('必要', 0.7999045252799988), ('地方政府', 0.7998923063278198), ('支持性', 0.7990859746932983), ('便利化', 0.7990027666091919), ('投保', 0.7989134788513184), ('投资政策', 0.798790454864502), ('惩戒', 0.7980732321739197), ('小额', 0.7976666688919067), ('扣除', 0.7965250611305237), ('货币政策', 0.7964276075363159), ('立项', 0.7957667708396912), ('额度', 0.7957634925842285), ('全国性', 0.7946979999542236), ('商检', 0.7944962382316589), ('守信', 0.7941418886184692), ('调动', 0.7924461364746094), ('不利于', 0.7918575406074524), ('期货市场', 0.7918171286582947), ('储备库', 0.7917209267616272), ('政府采购', 0.7915544509887695), ('界定', 0.7912067770957947), ('证券', 0.7911202907562256), ('纠正', 0.7905293107032776), ('筹集', 0.7898542284965515), ('法人', 0.7898436784744263), ('挂钩', 0.7893882989883423), ('出资', 0.7893537282943726)], [('平均不良', 0.9140143990516663), ('小微企业', 0.9059529900550842), ('债券', 0.8861731886863708), ('银行', 0.8711090683937073), ('票据', 0.8647992014884949), ('信用等级', 0.862526535987854), ('工具', 0.8594478368759155), ('信贷', 0.859072744846344), ('集合', 0.8545659780502319), ('不良', 0.8544822335243225), ('债务融资', 0.8500794172286987), ('收费权', 0.8445069193840027), ('抵押', 0.8431540727615356), ('再贴现', 0.8418576121330261), ('融资', 0.8412178754806519), ('证券化', 0.8411049246788025), ('货币政策', 0.8393687605857849), ('发行', 0.838211715221405), ('发行股票企业', 0.8362613320350647), ('知识产权质押', 0.8324614763259888), ('商业银行', 0.8321524858474731), ('授信', 0.8321102857589722), ('集合债', 0.8291148543357849), ('筹集', 0.8277400732040405), ('地方政府', 0.8263453245162964), ('资产', 0.8260819315910339), ('出口信用', 0.8238193988800049), ('海绵城市建设政府', 0.8218143582344055), ('质押', 0.8214487433433533), ('集合票据', 0.8193009495735168), ('可转换', 0.8187240362167358), ('特许经营权', 0.8184142708778381), ('募集', 0.8163701891899109), ('贴息', 0.815726101398468), ('私募', 0.8145632147789001), ('债权', 0.8142764568328857), ('担保', 0.8139758110046387), ('小额', 0.8123260736465454), ('券中期票据', 0.812134861946106), ('发行地方政府', 0.8117393851280212), ('拨备', 0.8109623789787292), ('政策性', 0.8106298446655273), ('风险补偿', 0.8101407885551453), ('绿色工程', 0.808687150478363), ('银行业', 0.808129072189331), ('发债', 0.8070896863937378), ('可获得性', 0.8070654273033142), ('两高一', 0.8070382475852966), ('资产证券化', 0.8067907094955444), ('股票', 0.8061023950576782), ('发放', 0.8020963668823242), ('租赁公司', 0.8012904524803162), ('证券', 0.8007955551147461), ('绿色金', 0.8007867336273193), ('预算', 0.8006294965744019), ('增信', 0.8002548813819885), ('合规', 0.7992495894432068), ('允许', 0.7991456389427185), ('短期', 0.7975980639457703), ('股权', 0.7959741950035095), ('税前', 0.7957857251167297), ('PPP', 0.7952741384506226), ('质押物', 0.7950325012207031), ('买方', 0.7949454188346863), ('林权', 0.7943703532218933), ('绿色企业', 0.7941948175430298), ('集体', 0.7938467860221863), ('操作性', 0.7905686497688293), ('法人', 0.7896010279655457), ('高收益', 0.7889462113380432), ('呆账', 0.788661003112793), ('上市公司', 0.7875493764877319), ('挂牌', 0.7862248420715332), ('审慎', 0.7851319909095764), ('专营', 0.7832935452461243), ('核销', 0.7830999493598938), ('出资', 0.78128582239151), ('支行', 0.7770622372627258), ('发行人', 0.77674400806427), ('专业性', 0.7739091515541077), ('信托', 0.7736532688140869), ('展期', 0.773488461971283), ('负债', 0.7729989290237427), ('资产负债率', 0.7723267078399658), ('公益性', 0.771503210067749), ('违约', 0.7706795930862427), ('入库', 0.7704213261604309), ('收益', 0.7702709436416626), ('银行间市场', 0.7701444625854492), ('办理', 0.7691617608070374)], [('牢牢', 0.8307128548622131), ('防范', 0.8251822590827942), ('系统性', 0.8156484365463257), ('底线', 0.8125852942466736), ('守住', 0.806307315826416), ('杠杆率', 0.7762089371681213), ('偿付', 0.7690280079841614), ('发生', 0.7571858167648315), ('潜在', 0.7553368210792542), ('定量分析', 0.7529809474945068), ('抵御', 0.7421631217002869), ('事件', 0.7344473004341125), ('稳妥', 0.7334461212158203), ('客户', 0.7308308482170105), ('信用风险', 0.7272106409072876), ('预警', 0.7211717367172241), ('操作性', 0.7154554724693298), ('务实', 0.7136452794075012), ('环境污染', 0.7115098834037781), ('分担', 0.7098478674888611), ('可持续', 0.7088894844055176), ('识别', 0.7083414196968079), ('防控', 0.707575261592865), ('发债', 0.7055097818374634), ('风险补偿', 0.701988160610199), ('突发事件', 0.7012307047843933), ('法人', 0.699260950088501), ('共担', 0.6965535283088684), ('高环境', 0.694814920425415), ('损害', 0.6925485730171204), ('风险评估', 0.6892513632774353), ('投保', 0.6874481439590454), ('预案', 0.685387372970581), ('突发', 0.683642566204071), ('质押物', 0.6824126243591309), ('披露', 0.6818563342094421), ('HSE', 0.6757559180259705), ('科学决策', 0.6756680607795715), ('负面', 0.6754887104034424), ('争端', 0.6735155582427979), ('事务', 0.673479437828064), ('合规', 0.6732085347175598), ('保险', 0.671450138092041), ('环境监管', 0.6706834435462952), ('准则', 0.6705775260925293), ('稳健', 0.670257568359375), ('全程', 0.6699444651603699), ('职业病', 0.6693608164787292), ('银行业', 0.6688880324363708), ('保证', 0.6680409908294678), ('可控', 0.6655722260475159), ('精准', 0.6614239811897278), ('申诉', 0.660652756690979), ('污染者', 0.659380316734314), ('从而', 0.6591682434082031), ('审评', 0.6578112840652466), ('严惩', 0.6573102474212646), ('避免', 0.6560519337654114), ('劳动者', 0.6559303998947144), ('相关者', 0.6550793051719666), ('诚实', 0.6549088954925537), ('杜绝', 0.6539706587791443), ('一项', 0.6537365317344666), ('守信', 0.652748167514801), ('持有人', 0.6506956815719604), ('造成', 0.6505665183067322), ('理性', 0.649858295917511), ('异常', 0.6498308181762695), ('追究', 0.6497818827629089), ('企业文化', 0.6494903564453125), ('限制类', 0.6491671800613403), ('连锁', 0.6468812823295593), ('满意度', 0.6467536091804504), ('绿色企业', 0.6466608643531799), ('法治', 0.6462882161140442), ('恶劣', 0.6452776789665222), ('概率', 0.6449894309043884), ('预审', 0.6448350548744202), ('援助', 0.6445611119270325), ('市场导向', 0.6445214748382568), ('商业银行', 0.6444674134254456), ('SIL', 0.6443591117858887), ('不良', 0.6442244648933411), ('信贷', 0.6439789533615112), ('内部', 0.6438503861427307), ('生命周期', 0.6438155770301819), ('预防', 0.6434192657470703), ('船员', 0.6433343291282654), ('合法', 0.6426347494125366), ('正常', 0.6422553062438965)], [('定价', 0.8192654848098755), ('排污权', 0.8151437640190125), ('水权', 0.7812281250953674), ('初始', 0.7808464169502258), ('结算', 0.7699261903762817), ('证书', 0.7689816951751709), ('碳排放权交易', 0.7687851190567017), ('有偿', 0.76253342628479), ('协议', 0.762145459651947), ('权用', 0.7574273347854614), ('自然资源', 0.7565705180168152), ('自愿', 0.755212128162384), ('市场价格', 0.7535869479179382), ('碳排放交易', 0.7522842288017273), ('分配', 0.7516012191772461), ('流转', 0.7498242855072021), ('交易所', 0.7421451210975647), ('可行性', 0.7390392422676086), ('竞价', 0.7364612221717834), ('理顺', 0.7317919731140137), ('盘查', 0.7302201390266418), ('审慎', 0.7301627397537231), ('征信', 0.7287040948867798), ('公开', 0.7272440195083618), ('林权', 0.7268800735473633), ('履约', 0.7266566157341003), ('确权', 0.7230432629585266), ('共享度', 0.7212314009666443), ('污染者', 0.7212282419204712), ('守信', 0.7208076119422913), ('时效性', 0.7201522588729858), ('全国性', 0.7201187610626221), ('煤价', 0.7194905281066895), ('现货', 0.7184031009674072), ('登记', 0.7180493474006653), ('碳排放权', 0.7161734104156494), ('配额', 0.7128932476043701), ('执业', 0.7122893333435059), ('规则', 0.7120846509933472), ('招标', 0.7119832634925842), ('长江绿融通', 0.7114695906639099), ('农户', 0.7112508416175842), ('期货', 0.7111461758613586), ('盘活', 0.7106982469558716), ('流向', 0.7103803753852844), ('备选', 0.7099980711936951), ('直销', 0.707791805267334), ('灵活', 0.7075824737548828), ('响应', 0.7073018550872803), ('托管', 0.7068250179290771), ('场外', 0.7066019177436829), ('证券', 0.7059468030929565), ('采信', 0.7054677605628967), ('小额', 0.7051945924758911), ('公证', 0.7051520347595215), ('保值', 0.7046903967857361), ('负面清单', 0.7046386003494263), ('跨境', 0.7020707130432129), ('平等', 0.7014681696891785), ('分销', 0.701431393623352), ('竞争性', 0.7005464434623718), ('记录', 0.7001388669013977), ('资产证券化', 0.6989920139312744), ('实效性', 0.6976230144500732), ('事务', 0.697620689868927), ('建筑设计', 0.6970333456993103), ('赤田水库', 0.6967753767967224), ('非资源型', 0.6963701844215393), ('黑名单', 0.6961522102355957), ('期货市场', 0.6956754922866821), ('项目库', 0.694159984588623), ('持有人', 0.6940482258796692), ('抵押', 0.6936075687408447), ('淡季', 0.6932893395423889), ('共担', 0.6925173401832581), ('行政区域', 0.6920160055160522), ('特许经营权', 0.6920133829116821), ('碳汇', 0.691361665725708), ('绿色金', 0.690445065498352), ('套期', 0.6900837421417236), ('分担', 0.6900671124458313), ('诚实', 0.6899935603141785), ('产权', 0.6897324919700623), ('行业性', 0.6896138191223145), ('收费权', 0.6892931461334229), ('经营者', 0.6891603469848633), ('审理', 0.6890646815299988), ('挂钩', 0.6889557838439941), ('欧盟', 0.6886337995529175), ('招投标', 0.6886088848114014)], [('惩罚性', 0.9256280064582825), ('阶梯电价', 0.883076012134552), ('收缴', 0.8818382024765015), ('修改', 0.8798125386238098), ('界定', 0.8780022263526917), ('期限', 0.8566638827323914), ('如有', 0.8552684187889099), ('颁布', 0.8477429747581482), ('暂行', 0.8444096446037292), ('峰谷', 0.8438066244125366), ('减免', 0.8422992825508118), ('依照', 0.8382192254066467), ('中央财政', 0.8377805352210999), ('有保有压', 0.8373486399650574), ('出台', 0.8371944427490234), ('改为', 0.8371114730834961), ('立项', 0.8368987441062927), ('未达标', 0.8344891667366028), ('惩处', 0.8322856426239014), ('重点用能单位节能管理', 0.831728994846344), ('期限内', 0.8316669464111328), ('惩罚', 0.8310782313346863), ('尽快', 0.830935537815094), ('2018', 0.8302453756332397), ('电费', 0.8298041224479675), ('资源性', 0.8295947909355164), ('规章', 0.8290558457374573), ('到位', 0.827725350856781), ('落地', 0.8262798190116882), ('管理暂行办法', 0.8255352973937988), ('规范性', 0.8251121044158936), ('财税', 0.8245299458503723), ('享受', 0.8243944048881531), ('缴纳', 0.8243657946586609), ('自行', 0.8222118020057678), ('目标责任制', 0.8212174773216248), ('清洁生产促进法', 0.8209760785102844), ('资源税', 0.8194000720977783), ('二强化', 0.8192910552024841), ('现行', 0.818519115447998), ('负起', 0.8178718686103821), ('足额', 0.8178125619888306), ('指令', 0.8168848752975464), ('矿权', 0.8162803649902344), ('经济政策', 0.8160898089408875), ('督查', 0.8135569095611572), ('取缔', 0.8133506178855896), ('严谨', 0.8129099607467651), ('下达', 0.811863362789154), ('参照', 0.8110487461090088), ('格式', 0.8098717927932739), ('死灰复燃', 0.8092818260192871), ('约谈', 0.8091139197349548), ('发改办', 0.8087974786758423), ('协议', 0.80859375), ('把好', 0.8084628582000732), ('行业电价', 0.8078333735466003), ('严把', 0.8077128529548645), ('合并', 0.8075847029685974), ('督促', 0.8074969053268433), ('区别对待', 0.8074655532836914), ('弄虚作假', 0.8063490390777588), ('应执行', 0.806138813495636), ('水土保持法', 0.8059682846069336), ('倒逼', 0.8055939674377441), ('常态化', 0.80516117811203), ('清费', 0.8051034212112427), ('落到', 0.8049842715263367), ('步骤', 0.804968535900116), ('环评', 0.8045297265052795), ('消费税', 0.8045003414154053), ('时间表', 0.804181694984436), ('奖惩', 0.8040745258331299), ('延续', 0.8036338686943054), ('水土保持', 0.8034417629241943), ('报批', 0.8032858967781067), ('对策', 0.8030403256416321), ('财力', 0.8030200004577637), ('前置', 0.8023648858070374), ('批评', 0.8019299507141113), ('拨付', 0.8011800646781921), ('听取', 0.8009592890739441), ('问责', 0.8006561994552612), ('阶梯', 0.8004319667816162), ('许可', 0.8003782629966736), ('回头看', 0.8000656366348267), ('区市', 0.7995826601982117), ('签订', 0.7991458773612976), ('有关方面', 0.7985022068023682), ('第六条', 0.7982667684555054)], [('外商', 0.8017461895942688), ('资本', 0.7930630445480347), ('天使', 0.7693743705749512), ('外资', 0.7613074779510498), ('私募', 0.7586866021156311), ('境内', 0.7552794814109802), ('股权', 0.7538249492645264), ('PPP', 0.7403358221054077), ('筹集', 0.7403032779693604), ('固定资产', 0.7344128489494324), ('核准', 0.7337142825126648), ('并购', 0.73092120885849), ('海绵城市建设政府', 0.7261219620704651), ('出资', 0.7258068919181824), ('贴息', 0.724503755569458), ('参股', 0.713922917842865), ('绿地', 0.7136138081550598), ('两高一', 0.7083642482757568), ('民营银行', 0.708216667175293), ('上市公司', 0.7080976366996765), ('挂牌', 0.7079339027404785), ('允许', 0.7077320218086243), ('补助', 0.7053614854812622), ('援助', 0.7044533491134644), ('政策性', 0.7037617564201355), ('发行股票企业', 0.7035712003707886), ('建厂', 0.7029715180397034), ('设立', 0.7028325796127319), ('科技型', 0.7011964321136475), ('基金', 0.6987894773483276), ('合资', 0.6986419558525085), ('预算', 0.6966025233268738), ('备案', 0.695642352104187), ('信用等级', 0.6938387751579285), ('入股', 0.6937471628189087), ('增信', 0.6933587789535522), ('绿色工程', 0.6928279995918274), ('形式', 0.6922666430473328), ('租赁公司', 0.6912323832511902), ('发起', 0.689949631690979), ('民营企业', 0.6896392107009888), ('第十章', 0.6891040205955505), ('股票', 0.6885414719581604), ('投向', 0.6883882880210876), ('票据', 0.6882412433624268), ('收费权', 0.6878272891044617), ('募集', 0.6876847743988037), ('援疆', 0.6876428723335266), ('可转换', 0.6833454966545105), ('融资', 0.6827392578125), ('创投', 0.682043731212616), ('同等', 0.6816551089286804), ('商业银行', 0.6814300417900085), ('证券化', 0.68086177110672), ('三板', 0.6805827021598816), ('集合票据', 0.6803199648857117), ('中小板', 0.6795127391815186), ('注入', 0.6790657043457031), ('风险补偿', 0.6789966821670532), ('发债', 0.6789849400520325), ('银行', 0.6786345839500427), ('发行地方政府', 0.6775702238082886), ('券中期票据', 0.6769311428070068), ('有限', 0.6768832206726074), ('集合债', 0.6765754818916321), ('换股', 0.6764821410179138), ('限制类', 0.6750306487083435), ('银行间市场', 0.6745443940162659), ('集合', 0.674123227596283), ('授信', 0.6721954345703125), ('创业板', 0.6721267104148865), ('场外', 0.6715089082717896), ('25倍', 0.6708559989929199), ('特许经营权', 0.6707576513290405), ('再贴现', 0.6704864501953125), ('更多', 0.6704635620117188), ('买方', 0.66974276304245), ('公益性', 0.6696107387542725), ('小微企业', 0.6694230437278748), ('增资', 0.6680269837379456), ('指引', 0.6679447889328003), ('主板', 0.6677916646003723), ('设定', 0.666873574256897), ('地方政府', 0.6658507585525513), ('任何', 0.6657775640487671), ('民营', 0.6656414866447449), ('便利化', 0.6654321551322937), ('撬动', 0.6650505065917969), ('船台', 0.6650310158729553), ('主销', 0.6648666858673096)], [('银行业', 0.799961268901825), ('机构', 0.7722399234771729), ('信贷', 0.7594095468521118), ('货币政策', 0.754845917224884), ('法人', 0.7536997199058533), ('商业银行', 0.7515062093734741), ('租赁公司', 0.747204065322876), ('专营', 0.7415985465049744), ('优异', 0.7393823862075806), ('事业部', 0.7352211475372314), ('分支机构', 0.7346077561378479), ('可持续', 0.7331477403640747), ('支行', 0.7330635190010071), ('担保', 0.7319130897521973), ('中介机构', 0.7309646010398865), ('晋商', 0.7303416132926941), ('发债', 0.7283435463905334), ('银行', 0.7269042730331421), ('再贴现', 0.7254443764686584), ('专业性', 0.7236327528953552), ('商业性', 0.7235636711120605), ('全国性', 0.7216628789901733), ('证券', 0.7165756225585938), ('共担', 0.7157880067825317), ('政策性', 0.7144250869750977), ('赣江新区', 0.7139950394630432), ('发行地方政府', 0.7134894728660583), ('开发性', 0.7123211026191711), ('风险补偿', 0.7117653489112854), ('实体', 0.7098616361618042), ('操作性', 0.7078524231910706), ('总部', 0.7076370716094971), ('信用等级', 0.7052205204963684), ('工具', 0.7050399780273438), ('债务融资', 0.7025013566017151), ('小额', 0.7017180919647217), ('短期', 0.7004846334457397), ('证券化', 0.6999730467796326), ('履职', 0.6992543339729309), ('业务', 0.6989032030105591), ('绿色金融', 0.6960123181343079), ('知识产权质押', 0.6960028409957886), ('村镇', 0.69599449634552), ('高管', 0.6954874396324158), ('民营银行', 0.6952758431434631), ('农户', 0.6951510906219482), ('脱实', 0.6950511932373047), ('撬动', 0.6947012543678284), ('出口信用', 0.6946640014648438), ('票据', 0.6945091485977173), ('收费权', 0.6945043206214905), ('改制', 0.6940237879753113), ('分行', 0.6936732530593872), ('绿色金', 0.6933194398880005), ('抵押', 0.692624568939209), ('林权', 0.6919576525688171), ('帮助', 0.6913692951202393), ('跨境', 0.6907312870025635), ('合规', 0.6901140809059143), ('投保', 0.6899771690368652), ('增信', 0.6890882253646851), ('业绩', 0.6873657703399658), ('上市公司', 0.6872982978820801), ('公益性', 0.6871830821037292), ('贷款', 0.6860979795455933), ('期货', 0.6860467195510864), ('多渠道', 0.6852926015853882), ('底线', 0.685119092464447), ('银企', 0.6850571632385254), ('事务', 0.6843547821044922), ('PPP', 0.6839578151702881), ('建筑师', 0.6838632225990295), ('允许', 0.6837117075920105), ('设立', 0.683691680431366), ('潜质', 0.6836127042770386), ('税务', 0.6834079623222351), ('资产证券化', 0.6826616525650024), ('债券', 0.68265700340271), ('港澳', 0.682589590549469), ('流动', 0.6823403239250183), ('买方', 0.6812311410903931), ('小微企业', 0.6810303926467896), ('助推', 0.6808866262435913), ('指引', 0.6806065440177917), ('金融政策', 0.6806014180183411), ('信托', 0.6804254055023193), ('信用风险', 0.6793419122695923), ('发行人', 0.6789991855621338), ('智库', 0.6781828999519348), ('评级', 0.6779963970184326)]]\n"]}], "source": ["# ----------- (4) generating keywords and detailed expression via word2vec (provided in the foleder: Part 2.0 keywords and detailed expresison.xlsx)_Python 3.10.8------------\n", "import pandas as pd\n", "import gensim\n", "from gensim.models import Word2Vec\n", "with open('Part 2.0 cutWords_list.txt',encoding=\"utf-8\") as file:\n", "    cutWords_list = [k.split() for k in file.readlines()]\n", "    keywords = ['碳排放', '强度', '节能', '能耗', '能效', '电价', '过剩产能', '落后产能', '产业', '发展规划', '科技创新', '技术创新', '研发', '问责', '禁止', '目标责任', '置换', '标准', '压减', '补贴', '财税', '贷款', '风险', '交易', '差别电价', '投资', '金融']\n", "    model = Word2Vec(cutWords_list ,sg=1,vector_size=300,window=10,min_count=2,workers=15,sample=1e-3)\n", "    similar0 = []\n", "    for key in keywords:\n", "        model.wv.get_vector(key)\n", "        similar = model.wv.most_similar(key, topn = 90)\n", "        similar0.append(similar)\n", "    print(similar0)\n", "    df1 = pd.DataFrame(keywords)\n", "    df2 = pd.DataFrame(similar0)\n", "    df3 = pd.concat([df1,df2],axis=1)\n", "    df3_1=df3.T \n", "    df3_1.to_excel(\"./Part 2.0 keywords and detailed expresison.xlsx\", engine='xlsxwriter')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 2.1 Policy objective classification"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# ----------- (1) calculating phrase frequancy for different policy objectives (contents) _Python 3.10.8------------\n", "import re\n", "import os\n", "from zhon.hanzi import punctuation\n", "from LAC import LAC\n", "import pandas as pd\n", "import collections\n", "\n", "path = './policy texts (10 policies for coding)/Policy_objective/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "#Define function-\"get_words\" for texts aggragation\n", "def get_words(policy):\n", "    word = ''\n", "    with open(path +  policy, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            line = line.replace(\" \",\"\")\n", "            word += line\n", "    return word\n", "\n", "#Define function-\"get_tokenize\" for  phrase segmantation based on LAC (paddlepaddle, Baidu)\n", "def get_tokenize(word):\n", "    string = re.sub(\"[{}]+\".format(punctuation), \"\", word)\n", "    string1 = string.replace('--', '').replace('[', '')\n", "    lac = LAC(mode = 'seg')\n", "    lac.load_customization('Part 2.1-(1) Objective_content_lexicon.txt', sep=None)\n", "    seg_result = lac.run(string1)\n", "    return seg_result\n", "\n", "#Define function-\"word_freq\" for calculationg the frequency of key phrases\n", "def word_freq(seg_result):\n", "    E = []\n", "    C = []\n", "    CapU = []\n", "    T = []\n", "    E_k = []\n", "    C_k = []\n", "    CU_k = []\n", "    \n", "    #Key phrases (please refer to \"Part 2 Lexicons for policy classification.xlsx\")\n", "    Energy = ['能耗', '能源消费', '能源利用效率', '能效']\n", "    Carbon = ['碳', '温室气体']\n", "    CU = ['产能']\n", "    Tech = ['节能环保产业', '关键技术', '技术研发', '科技创新', '产业规划', '技术改造', '装备自主', '技术产业化', '产业规模', '创新能力', '生产成本', '主营业务收入', '核心技术', '竞争力', '生产能力', '技术创新', '研发投入', '研发机构', '研发人员', '科技成果']\n", "    # specific key phrases for energy conservation\n", "    E_key = ['差别电价','阶梯电价', '能耗年均降低', '能耗低于', '能耗下降', '能耗降低','能耗比', '能源消耗比', '能源消耗较', '能耗小于', '能耗分别比', '综合能耗', '能源消费总量', '综合平均能耗','能耗总量', 'GDP能耗','能源消耗水平', '能耗累计下降','产值能耗','能源消耗总量', '总产量的比例', '总量比例', '生产节能', '能耗分别下降', '节能量', '能源消耗年均下降', '能耗年均下降', '万元生产总值能耗', '能源消耗降低', '能耗降低']\n", "    # specific key phrases for carbon reduction\n", "    C_key = ['二氧化碳排放下降', '二氧化碳排放量下降', '二氧化碳排放降低', '二氧化碳排放量降低', '二氧化碳排放减少', '二氧化碳排放量减少', '二氧化碳排放分别','二氧化碳排放量分别','二氧化碳排放量比', '二氧化碳排放比', '二氧化碳排放量分别比', '二氧化碳排放分别比', '碳排放强度下降', '碳排放强度降低', '碳排放强度减少', '绿色金融', '万元生产总值二氧化碳排放', '碳排放量均下降']\n", "    # specific key phrases for capacity utilization\n", "    CU_key = ['过剩产能', '淘汰关停', '落后', '去产能']\n", "\n", "    for i in seg_result:\n", "        for x in Energy:\n", "            if x in i:\n", "                E.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in Carbon:\n", "            if x in i:\n", "                C.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in CU:\n", "            if x in i:\n", "                CapU.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in Tech:\n", "            if x in i:\n", "                T.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in E_key:\n", "            if x in i:\n", "                E_k.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in C_key:\n", "            if x in i:\n", "                C_k.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in CU_key:\n", "            if x in i:\n", "                CU_k.append(x)\n", "    \n", "    E_counts = collections.Counter(E) \n", "    C_counts = collections.Counter(C) \n", "    CU_counts = collections.Counter(CapU) \n", "    Tech_counts = collections.Counter(T) \n", "    Ek_counts = collections.Counter(E_k) \n", "    Ck_counts = collections.Counter(C_k) \n", "    CUk_counts = collections.Counter(CU_k) \n", "    \n", "    E_sum = sum(E_counts.values())\n", "    C_sum = sum(C_counts.values())\n", "    CU_sum = sum(CU_counts.values())\n", "    Tech_sum = sum(Tech_counts.values())\n", "    Ek_sum = sum(Ek_counts.values())\n", "    Ck_sum = sum(Ck_counts.values())\n", "    CUk_sum = sum(CUk_counts.values())\n", "    return E_sum, C_sum, CU_sum, Tech_sum, Ek_sum, Ck_sum, CUk_sum, Ek_counts, Ck_counts, CUk_counts\n", "\n", "#Calculating phrase frequency for policy objective texts' content\n", "save_data=[]\n", "for filename in path_list:\n", "    word = get_words(filename)\n", "    seg_result = get_tokenize(word)\n", "    E_sum, C_sum, CU_sum, Tech_sum, Ek_sum, Ck_sum, CUk_sum, Ek_counts, Ck_counts, CUk_counts= word_freq(seg_result)\n", "    dic = {'filename': filename, 'EnergyC': E_sum, 'CarbonR': C_sum, 'CapacityU': CU_sum, 'Technology': Tech_sum, 'E_key': Ek_sum, 'C_key': Ck_sum, 'CU_key': CUk_sum, 'E_k': Ek_counts, 'C_k': Ck_counts, 'CU_k': CUk_counts}\n", "    save_data.append(dic)\n", "\n", "df = pd.DataFrame(save_data)\n", "df.to_excel(\"./policy texts (10 policies for coding)/Part 2.1 Policy_objective_content.xlsx\", engine='xlsxwriter')\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# ----------- (2) calculating phrase frequancy for different policy objectives (titles) _Python 3.10.8------------\n", "\n", "import os\n", "from zhon.hanzi import punctuation\n", "from LAC import LAC\n", "import pandas as pd\n", "import collections\n", "\n", "path1 = './policy texts (10 policies for coding)/Full_text/'\n", "path_list1=os.listdir(path1)\n", "path_list1.sort() \n", "\n", "#Define function-\"get_title\" for calculationg the frequency of key phrases in policy title\n", "def get_title(policy1):\n", "    with open(path1 +  policy1, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        lines = f.readlines()\n", "        policy1 = lines[2].strip()\n", "        lac = LAC(mode = 'seg')\n", "        lac.load_customization('Part 2.1-(2) Objective_title_lexicon.txt', sep=None)\n", "        seg_policy = lac.run(policy1)\n", "        #key phrases (please refer to \"Part 2 Lexicons for policy classification.xlsx\")\n", "        Title_Tech= ['发展规划', '调整和振兴规划','高质量发展', '发展“十三五”规划', '发展“十二五”规划', '“十二五”发展规划',  '“十三五”发展规划', '推广', '产业发展', '示范', '科技创新', '技术改造', '产业健康发展']\n", "        Title_CU= ['产能过剩', '产能严重过剩', '淘汰落后', '产能过快增长', '过剩产能', '淘汰', '项目清理', '大气污染治理', '蓝天保卫战', '关停取缔', '退出产能']\n", "        Title_C= ['低碳','绿色金融', '生态文明', '低碳城市', '低碳试点', '碳排放', '碳达峰', '气候变化', '生态']\n", "        Title_E= ['污染物','节能','差别电价','万家企业', '目标责任', '行业电价', '阶梯电价', '价格手段']\n", "\n", "        Tech_title = []\n", "        CU_title = []\n", "        C_title = []\n", "        E_title = []\n", "\n", "        for i in seg_policy:\n", "            for x in Title_Tech:\n", "                if x in i:\n", "                    Tech_title.append(x)\n", "\n", "        for i in seg_policy:\n", "            for x in Title_CU:\n", "                if x in i:\n", "                    CU_title.append(x)\n", "\n", "        for i in seg_policy:\n", "            for x in Title_C:\n", "                if x in i:\n", "                    C_title.append(x)\n", "        \n", "        for i in seg_policy:\n", "            for x in Title_E:\n", "                if x in i:\n", "                    E_title.append(x)\n", "            \n", "        TitleTech_counts = collections.Counter(Tech_title) \n", "        TitleC_counts = collections.Counter(C_title) \n", "        TitleCU_counts = collections.Counter(CU_title) \n", "        TitleE_counts = collections.Counter(E_title) \n", "\n", "        TitleTech_sum = sum(TitleTech_counts.values())\n", "        TitleC_sum = sum(TitleC_counts.values())\n", "        TitleE_sum = sum(TitleE_counts.values())\n", "        TitleCU_sum = sum(TitleCU_counts.values())\n", "    return policy1, TitleE_sum, TitleC_sum, TitleCU_sum, TitleTech_sum, TitleE_counts, TitleC_counts, TitleCU_counts, TitleTech_counts\n", "\n", "#Calculating phrase frequency for policy objective texts' title \n", "save_data1=[]\n", "for filename1 in path_list1:\n", "    policy1, TitleE_sum, TitleC_sum, TitleCU_sum, TitleTech_sum, TitleE_counts, TitleC_counts, TitleCU_counts, TitleTech_counts= get_title(filename1)\n", "    dic1 = {'filename': 'G_'+filename1, 'policyname': policy1, 'Title_EnergyC': TitleE_sum, 'Title_CarbonR': TitleC_sum, 'Title_CapacityU': TitleCU_sum, 'Title_Tech': TitleTech_sum, 'E': TitleE_counts, 'C': TitleC_counts, 'CU': TitleCU_counts, 'Tech': TitleTech_counts}\n", "    save_data1.append(dic1)\n", "\n", "df1 = pd.DataFrame(save_data1)\n", "df1.to_excel(\"./policy texts (10 policies for coding)/Part 2.1 Policy_objective_title.xlsx\", engine='xlsxwriter')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (3) Policy objective classification based on title and content _Stata 15------------\n", "\n", "#This part needs to be executed in Stata 15.\n", "#The do file version (\"Part 2.1-(3) Policy objective classification based on title and content.do\") for Stata 15 is saved in Folder-\"Low_carbon_policy_intensity_code\".\n", "\n", "''' \n", "cd \"C:\\...\\Low_carbon_policy_intensity_code\\policy texts (10 policies for coding)\"\n", "clear\n", "*Import and merge files: \"Part 2.1 Policy_objective_content\", \"Part 2.1 Policy_objective_title.dta\"\n", "import excel \"Part 2.1 Policy_objective_content.xlsx\",sheet(\"Sheet1\") firstrow\n", "save \"Part 2.1 Policy_objective_content.dta\"\n", "import excel \"Part 2.1 Policy_objective_title.xlsx\", sheet(\"Sheet1\") firstrow clear\n", "save \"Part 2.1 Policy_objective_title.dta\"\n", "clear\n", "\n", "use \"Part 2.1 Policy_objective_content.dta\"\n", "merge 1:1 filename using \"Part 2.1 Policy_objective_title.dta\"\n", "drop _merge\n", "save \"Part 2.1 Policy_objective_all.dta\"\n", "\n", "*Generate grouping variable\n", "gen Energy_group=0\n", "gen Carbon_group=0\n", "gen Capacity_group=0\n", "gen Tech_group=0\n", "\n", "*Calculcate the max frequency among 4 groups for each policy\n", "egen Max=rmax(EnergyC CarbonR CapacityU Technology)\n", "\n", "*1. Grouping policies by title frequency if all content frequencies euqal to 0.\n", "replace Energy_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0 & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC !=0 & Title_CarbonR==0 & Title_CapacityU==0 & Title_Tech==0\n", "replace Carbon_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0  & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC==0 & Title_CarbonR !=0 & Title_CapacityU==0 & Title_Tech==0\n", "replace Capacity_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0  & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC==0 & Title_CarbonR==0 & Title_CapacityU !=0 & Title_Tech==0\n", "replace Tech_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0  & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC==0 & Title_CarbonR==0 & Title_CapacityU==0 & Title_Tech !=0\n", "\n", "*2. Grouping Carbon_reduction based on C_key (with specific quantitive carbon reduction goals)\n", "replace Carbon_group=1 if C_key != 0\n", "\n", "*3. Grouping Energy_conservation\n", "replace Energy_group=1 if Carbon_group==0 & E_key !=0\n", "replace Energy_group=1 if Carbon_group==0 & C_key==0 & E_key==0 & CU_key==0 & Max==EnergyC & Max !=0\n", "  *trade-off between carbon_group and energy_group: carbon_group with top priority\n", "replace Carbon_group=1 if Max==CarbonR  & Max !=0 & CarbonR !=EnergyC &Energy_group==0\n", "\n", "*4. Grouping Capacity_utilization\n", "replace Capacity_group=1 if Carbon_group==0 & Energy_group==0 & CU_key > 1\n", "replace Capacity_group=1 if Carbon_group==0 & Energy_group==0 & CU_key <= 1 & Max==CapacityU & Max !=0\n", "\n", "*5. Grouping Technology\n", "replace Tech_group=1 if Carbon_group==0 & Energy_group==0 & Capacity_group==0 & Max==Technology & Max !=0\n", "\n", "*6. Manual checking for policies with no group (Directly change the number from 0 to 1 in columns of EnergyC, CarbonR, CapacityU, and Technology)\n", "gen Manual=1 if Energy_group==0 & Carbon_group==0 & Capacity_group==0 & Tech_group==0\n", "\n", "replace Energy_group=1 if EnergyC==1 & Manual==1\n", "replace Carbon_group=1 if CarbonR==1 & Manual==1\n", "replace Capacity_group=1 if CapacityU==1 & Manual==1\n", "replace Tech_group=1 if Technology==1 & Manual==1\n", "\n", "*7. Generate new file_name\n", "gen filename_new =\"\"\n", "replace filename_new=\"E_\"+filename if Energy_group==1\n", "replace filename_new=\"C_\"+filename if Carbon_group==1\n", "replace filename_new=\"CU_\"+filename if Capacity_group==1\n", "replace filename_new=\"Tech_\"+filename if Tech_group==1\n", "\n", "*8. Recheck whether all the policies are classified into only one group.\n", "gen sum= Carbon_group+Energy_group+Capacity_group+Tech_group\n", "sum sum\n", "drop sum\n", "\n", "*Generate rename pattern\n", "gen rename=\"ren \"+filename+\" \"+ filename_new\n", "order filename_new rename,after(filename)\n", "export excel using \"Part 2.1 Policy_objective_Classification_FINAL.xlsx\", firstrow(variables)\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Rename filename for policy objectives based on column_\"rename\" in \"Part 2.1 Policy_objective_Classification_FINAL.xlsx\"\n", "# Generate a txt file with contents below in \"./policy texts (10 policies for coding)/Policy_objective/\", save it as .bat(ANSI) and execute.\n", "'''\n", "ren G_city_1.txt E_G_city_1.txt\n", "ren G_city_2.txt E_G_city_2.txt\n", "ren G_city_3.txt Tech_G_city_3.txt\n", "ren G_city_4.txt CU_G_city_4.txt\n", "ren G_city_5.txt CU_G_city_5.txt\n", "ren G_city_6.txt CU_G_city_6.txt\n", "ren G_city_7.txt E_G_city_7.txt\n", "ren G_city_8.txt C_G_city_8.txt\n", "ren G_city_9.txt Tech_G_city_9.txt\n", "ren G_city_10.txt C_G_city_10.txt\n", "'''"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 2.2 Policy instrument classification"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# ----------- (1) calculating phrase frequancy for different policy instruments (contents) _Python 3.10.8------------\n", "\n", "import re\n", "import os\n", "from zhon.hanzi import punctuation\n", "from LAC import LAC\n", "import pandas as pd\n", "import collections\n", "\n", "path = './policy texts (10 policies for coding)/Policy_instrument/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "#Define function-\"get_words\" for texts aggragation\n", "def get_words(policy):\n", "    word = ''\n", "    with open(path +  policy, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            line = line.replace(\" \",\"\")\n", "            word += line\n", "    return word, policy\n", "\n", "#Define function-\"get_tokenize\" for  phrase segmantation based on LAC (paddlepaddle, Baidu)\n", "def get_tokenize(word):\n", "    string = re.sub(\"[{}]+\".format(punctuation), \"\", word)\n", "    string1 = string.replace('--', '').replace('[', '')\n", "    lac = LAC(mode = 'seg')\n", "    lac.load_customization('Part 2.2-(1) Instrument_lexicon.txt', sep=None)\n", "    seg_result = lac.run(string1)\n", "    return seg_result\n", "\n", "#Define function-\"word_freq\" for calculationg the frequency of key phrases\n", "def word_freq(seg_result):\n", "    #command-and-control instruments\n", "    Regulation = ['停业整顿', '关停', '关闭', '问责', '责任', '追究', '处分', '整改', '罚款', '处罚', '注销', '吊销', '准入', \\\n", "                '禁止', '新上', '进入', '置换', '替代', '淘汰', '兼并', '重组', '压减', '强制公开', \\\n", "                '负面清单', '核准', '去产能', '严查', '倒逼', '上大压小', '任务分解', '标准', '目标责任考核', '目标责任评价', '重点用能单位节能管理', '目标分解方案']\n", "\n", "    #market-based instruments\n", "    Finance = ['补偿', '电价', '资金', '投资', '预算', '财政', '奖励', '贷款', '贴息', '金融', '融资', '债券', '基金', '担保', '保险', \\\n", "            '资本', '股权', '信贷', '价格措施', '财税', '投入', '税收', '补贴', '免税', '风险补偿',\\\n", "            '债务融资', '减免', '优惠', '碳排放权交易', '碳市场', '配额', '碳排放报告', '碳排放核查', '碳排放交易', '专项资金']\n", "\n", "    Regulation_instrument = []\n", "    Finance_instrument = []\n", "\n", "    for i in seg_result:\n", "        for x in Regulation:\n", "            if x in i:\n", "                Regulation_instrument.append(x)\n", "\n", "    for i in seg_result:\n", "        for x in Finance:\n", "            if x in i:\n", "                Finance_instrument.append(x)\n", "       \n", "    Reg_counts = collections.Counter(Regulation_instrument) \n", "    Fin_counts = collections.Counter(Finance_instrument) \n", "\n", "    Reg_sum = sum(Reg_counts.values())\n", "    Fin_sum = sum(Fin_counts.values())\n", "    return Reg_counts, Fin_counts, Reg_sum, Fin_sum\n", "\n", "#Calculating phrase frequency for policy instrument texts' content\n", "save_data=[]\n", "for filename in path_list:\n", "    word, policy = get_words(filename)\n", "    seg_result = get_tokenize(word)\n", "    Reg_counts, Fin_counts, Reg_sum, Fin_sum= word_freq(seg_result)\n", "    dic = {'filename': policy, 'CC_freq': Reg_sum, 'MB_freq': Fin_sum, 'CC_key': Reg_counts, 'MB_key': Fin_counts}\n", "    save_data.append(dic)\n", "\n", "df = pd.DataFrame(save_data)\n", "df.to_excel(\"./policy texts (10 policies for coding)/Part 2.2 Policy_instrument_content.xlsx\", engine='xlsxwriter')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# ----------- (2) calculating phrase frequancy for different policy instruments (titles) _Python 3.10.8------------\n", "\n", "import os\n", "from zhon.hanzi import punctuation\n", "from LAC import LAC\n", "import pandas as pd\n", "import collections\n", "\n", "path1 = './policy texts (10 policies for coding)/Full_text/'\n", "path_list1=os.listdir(path1)\n", "path_list1.sort() \n", "\n", "#Define function-\"get_title\" for calculationg the frequency of key phrases in policy title\n", "def get_title1(policy1):\n", "    with open(path1 +  policy1, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        lines = f.readlines()\n", "        policy1 = lines[2].strip()\n", "        lac = LAC(mode = 'seg')\n", "        lac.load_customization('Part 2.2-(1) Instrument_lexicon.txt', sep=None)\n", "        seg_policy = lac.run(policy1)\n", "        #command-and-control keywords\n", "        Regulation = ['停业整顿', '关停', '关闭', '问责', '责任', '追究', '处分', '整改', '罚款', '处罚', '注销', '吊销', '准入', \\\n", "                    '禁止', '新上', '进入', '置换', '替代', '淘汰', '兼并', '重组', '压减', '强制公开', \\\n", "                    '负面清单', '核准', '去产能', '严查', '倒逼', '上大压小', '任务分解', \\\n", "                    '标准', '目标责任考核', '目标责任评价', '重点用能单位节能管理', '目标分解方案', '部门分工', '节能目标', '发展计划', \\\n", "                    '指标计划', '发展规划', '考核指标', '目标分解', '分工落实']\n", "\n", "        #market-based keywords\n", "        Finance = ['补偿', '电价', '资金', '投资', '预算', '财政', '奖励', '贷款', '贴息', '金融', '融资', '债券', '基金', '担保', '保险', \\\n", "                '资本', '股权', '信贷', '价格措施', '财税', '投入', '税收', '补贴', '免税', '风险补偿',\\\n", "                '债务融资', '减免', '优惠', '碳排放权交易', '碳市场', '配额', '碳排放报告', '碳排放核查', '碳排放交易', '专项资金']\n", "\n", "\n", "        Regulation_instrument = []\n", "        Finance_instrument = []\n", "\n", "        for i in seg_policy:\n", "            for x in Regulation:\n", "                if x in i:\n", "                    Regulation_instrument.append(x)\n", "\n", "        for i in seg_policy:\n", "            for x in Finance:\n", "                if x in i:\n", "                    Finance_instrument.append(x)\n", "      \n", "        Reg_counts = collections.Counter(Regulation_instrument) \n", "        Fin_counts = collections.Counter(Finance_instrument) \n", "            \n", "        Reg_sum = sum(Reg_counts.values())\n", "        Fin_sum = sum(Fin_counts.values())\n", "    return policy1, Reg_counts, Fin_counts, Reg_sum, Fin_sum\n", "\n", "\n", "save_data1=[]\n", "for filename1 in path_list1:\n", "    policy1, Reg_counts, Fin_counts, Reg_sum, Fin_sum= get_title1(filename1)\n", "    dic1 = {'filename': \"A_\"+filename1, 'policyname': policy1, 'CC_title': Reg_sum, 'MB_title': Fin_sum, 'CC_titlekey': Reg_counts, 'MB_titlekey': Fin_counts}\n", "    save_data1.append(dic1)\n", "\n", "df1 = pd.DataFrame(save_data1)\n", "df1.to_excel(\"./policy texts (10 policies for coding)/Part 2.2 Policy_instrument_title.xlsx\", engine='xlsxwriter')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------- (3) Policy instrument classification based on title and content _Stata 15------------\n", "\n", "#This part needs to be executed in Stata 15.\n", "#The do file version (\"Part 2.2-(3) Policy instrument classification based on title and content.do\") for Stata 15 is saved in Folder-\"Low_carbon_policy_intensity_code\".\n", "\n", "''' \n", "cd \"C:\\...\\Low_carbon_policy_intensity_code\\policy texts (10 policies for coding)\"\n", "clear\n", "*Import and merge files: \"Part 2.1 Policy_instrument_content\", \"Part 2.1 Policy_instrument_title.dta\"\n", "import excel \"Part 2.2 Policy_instrument_content.xlsx\",sheet(\"Sheet1\") firstrow\n", "save \"Part 2.2 Policy_instrument_content.dta\"\n", "import excel \"Part 2.2 Policy_instrument_title.xlsx\", sheet(\"Sheet1\") firstrow clear\n", "save \"Part 2.2 Policy_instrument_title.dta\"\n", "clear\n", "\n", "use \"Part 2.2 Policy_instrument_content.dta\"\n", "merge 1:1 filename using \"Part 2.2 Policy_instrument_title.dta\"\n", "drop _merge\n", "save \"Part 2.2 Policy_instrument_all.dta\"\n", "\n", "*Generate grouping variable\n", "gen CC_instrument =0\n", "gen MB_instrument =0\n", "gen Composite_instrument=0\n", "gen gap=abs(MB_freq-CC_freq)\n", "\n", "*1. Grouping by CC_freq and MB_freq. Gap is used for composite instrument\n", "replace CC_instrument=1 if  CC_freq>MB_freq & gap>=10\n", "replace MB_instrument=1 if  CC_freq<MB_freq & gap>=10\n", "\n", "*2. Group composite instruments\n", "replace Composite_instrument=1 if CC_freq>=10 & MB_freq>=10 & gap<10\n", "\n", "*3. Other situations\n", "replace CC_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq>0 & MB_freq==0\n", "replace MB_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq==0 & MB_freq>0\n", "\n", "*4. If CC_freq and MB_freq equal to 0, consider title frequencies\n", "replace CC_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq==0 & MB_freq==0 & CC_titlekey>MB_titlekey\n", "replace MB_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq==0 & MB_freq==0 & CC_titlekey<MB_titlekey\n", "\n", "*5. Remaining situation\n", "replace CC_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq>MB_freq\n", "replace MB_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq<MB_freq\n", "\n", "*6. Manually check  (Directly change the number from 0 to 1 in columns of CC_instrument, MB_instrument, Composite_instrument)\n", "gen Manual=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0\n", "\n", "replace CC_instrument=1 if CC_instrument==1 & Manual==1\n", "replace MB_instrument=1 if MB_instrument==1 & Manual==1\n", "replace Composite_instrument=1 if Composite_instrument==1 & Manual==1\n", "\n", "*7. Recheck whether all the policies are classified into only one group.\n", "gen sum_new= CC_instrument+MB_instrument+Composite_instrument\n", "sum sum_new\n", "drop sum_new\n", "\n", "*Generate rename pattern\n", "gen filename_new=\"\"\n", "replace filename_new=\"CC_\"+ filename if CC_instrument==1\n", "replace filename_new=\"MB_\"+ filename if MB_instrument==1\n", "replace filename_new=\"Composite_\"+ filename if Composite_instrument==1\n", "\n", "gen rename=\"ren \"+filename+\" \"+ filename_new\n", "order filename_new rename,after(filename)\n", "export excel using \"Part 2.2 Policy_instrument_Classification_FINAL.xlsx\", firstrow(variables)\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Rename filename for policy instruments based on column_\"rename\" in \"Part 2.2 Policy_instrument_Classification_FINAL.xlsx\"\n", "# Generate a txt file with contents below in \"./policy texts (10 policies for coding)/Policy_instrument/\", save it as .bat(ANSI) and execute.\n", "\n", "'''\n", "ren A_city_1.txt MB_A_city_1.txt\n", "ren A_city_2.txt MB_A_city_2.txt\n", "ren A_city_3.txt MB_A_city_3.txt\n", "ren A_city_4.txt Composite_A_city_4.txt\n", "ren A_city_5.txt CC_A_city_5.txt\n", "ren A_city_6.txt CC_A_city_6.txt\n", "ren A_city_7.txt CC_A_city_7.txt\n", "ren A_city_8.txt CC_A_city_8.txt\n", "ren A_city_9.txt MB_A_city_9.txt\n", "ren A_city_10.txt MB_A_city_10.txt\n", "'''"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Part 3: Labelling preparation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 3.1 Construct new folders for different policy objectives and instruments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# -----------Construct new folders for four objectives and move relevant text files _Python 3.10.8------------\n", "import os\n", "import shutil\n", "from glob import glob\n", "\n", "#Define function\"mkdir\" to construct function\n", "def mkdir(path):\n", "\tfolder = os.path.exists(path)\n", "\tif not folder:                   \n", "\t\tos.makedirs(path) \n", "\t\tprint(\"---  new folder...  ---\")\n", "\t\tprint(\"---  OK  ---\")\n", "\telse:\n", "\t\tprint(\"---  There is this folder!  ---\")\n", "\t\t\n", "fileG1 = \"./policy texts (10 policies for coding)/Policy_objective/Carbon/\"\n", "fileG2 = \"./policy texts (10 policies for coding)/Policy_objective/Energy/\"\n", "fileG3 = \"./policy texts (10 policies for coding)/Policy_objective/CU/\"\n", "fileG4 = \"./policy texts (10 policies for coding)/Policy_objective/Tech/\"\n", "mkdir(fileG1)\n", "mkdir(fileG2)\n", "mkdir(fileG3)\n", "mkdir(fileG4)\n", "\n", "# -----------Move text files for different policy objective to corresponding folder_Python 3.10.8------------\n", "\n", "#Define function to move files to corresponding folders\n", "def mymovefile(srcfile,dstpath):                 \n", "    if not os.path.isfile(srcfile):\n", "        print (\"%s not exist!\"%(srcfile))\n", "    else:\n", "        fpath, fname=os.path.split(srcfile)  \n", "        if not os.path.exists(dstpath):\n", "            os.makedirs(dstpath)                    \n", "        shutil.move(srcfile, dstpath + fname) \n", "        print (\"move %s -> %s\"%(srcfile, dstpath + fname))\n", " \n", "\n", "#Original folder\n", "src_dir = './policy texts (10 policies for coding)/Policy_objective/'\n", "#Target forlder\n", "dst_dir1 = './policy texts (10 policies for coding)/Policy_objective/Carbon/'\n", "dst_dir2 = './policy texts (10 policies for coding)/Policy_objective/Energy/'\n", "dst_dir3 = './policy texts (10 policies for coding)/Policy_objective/CU/'\n", "dst_dir4 = './policy texts (10 policies for coding)/Policy_objective/Tech/'\n", "\n", "#Corresponding files\n", "src_file_list1 = glob(src_dir + 'C_*') \n", "src_file_list2 = glob(src_dir + 'E_*') \n", "src_file_list3 = glob(src_dir + 'CU_*') \n", "src_file_list4 = glob(src_dir + 'Tech_*') \n", "\n", "#Move files\n", "for srcfile1 in src_file_list1:\n", "    mymovefile(srcfile1, dst_dir1) \n", "for srcfile2 in src_file_list2:\n", "    mymovefile(srcfile2, dst_dir2) \n", "for srcfile3 in src_file_list3:\n", "    mymovefile(srcfile3, dst_dir3) \n", "for srcfile4 in src_file_list4:\n", "    mymovefile(srcfile4, dst_dir4) "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# -----------Construct new folders for three instruments and move relevant text files _Python 3.10.8------------\n", "import os\n", "import shutil\n", "from glob import glob\n", "\n", "#Define function\"mkdir\" to construct function\n", "def mkdir(path):\n", "\tfolder = os.path.exists(path)\n", "\tif not folder:                   \n", "\t\tos.makedirs(path) \n", "\t\tprint(\"---  new folder...  ---\")\n", "\t\tprint(\"---  OK  ---\")\n", "\telse:\n", "\t\tprint(\"---  There is this folder!  ---\")\n", "\t\t\n", "fileA1 = \"./policy texts (10 policies for coding)/Policy_instrument/CC/\"\n", "fileA2 = \"./policy texts (10 policies for coding)/Policy_instrument/MB/\"\n", "fileA3 = \"./policy texts (10 policies for coding)/Policy_instrument/Composite/\"\n", "\n", "mkdir(fileA1)\n", "mkdir(fileA2)\n", "mkdir(fileA3)\n", "\n", "# -----------Move text files for different policy instruments to corresponding folder_Python 3.10.8------------\n", "\n", "#Define function to move files to corresponding folders\n", "def mymovefile(srcfile,dstpath):                 \n", "    if not os.path.isfile(srcfile):\n", "        print (\"%s not exist!\"%(srcfile))\n", "    else:\n", "        fpath, fname=os.path.split(srcfile)  \n", "        if not os.path.exists(dstpath):\n", "            os.makedirs(dstpath)                    \n", "        shutil.move(srcfile, dstpath + fname) \n", "        print (\"move %s -> %s\"%(srcfile, dstpath + fname))\n", " \n", "\n", "#Original folder\n", "src_dir = './policy texts (10 policies for coding)/Policy_instrument/'\n", "#Target forlder\n", "dst_dir1 = './policy texts (10 policies for coding)/Policy_instrument/CC/'\n", "dst_dir2 = './policy texts (10 policies for coding)/Policy_instrument/MB/'\n", "dst_dir3 = './policy texts (10 policies for coding)/Policy_instrument/Composite/'\n", "\n", "#Corresponding files\n", "src_file_list1 = glob(src_dir + 'CC_*') \n", "src_file_list2 = glob(src_dir + 'MB_*') \n", "src_file_list3 = glob(src_dir + 'Composite_*') \n", "\n", "#Move files\n", "for srcfile1 in src_file_list1:\n", "    mymovefile(srcfile1, dst_dir1) \n", "for srcfile2 in src_file_list2:\n", "    mymovefile(srcfile2, dst_dir2) \n", "for srcfile3 in src_file_list3:\n", "    mymovefile(srcfile3, dst_dir3) \n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 3.2 Extract main texts of objctives for manual labelling (<=512 words)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# -----------Extracting main texts for carbon reduction objective _Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "\n", "path = './policy texts (10 policies for coding)/Policy_objective/Carbon/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "#Define keywords (containing keywords with carbon and energy)\n", "C_keynew = ['二氧化碳排放总量', '二氧化碳排放下降', '二氧化碳排放量下降', '二氧化碳排放降低', '二氧化碳排放量降低', '二氧化碳排放减少', '二氧化碳排放量减少', '二氧化碳排放分别','二氧化碳排放量分别','二氧化碳排放量比', '二氧化碳排放比', '二氧化碳排放量分别比', '二氧化碳排放分别比', '碳排放强度下降', '碳排放强度降低', '碳排放强度减少', '生产总值二氧化碳排放', '碳排放量均下降']\n", "C_key = ['碳', '碳排放', '碳强度', '绿色金融', '温室气体', '生态', '绿色']\n", "E_key = ['能耗年均降低', '能耗低于', '能耗下降', '能耗降低','能耗比', '能源消耗比', '能源消耗较', '能耗小于', '能耗分别比', '综合能耗', '能源消费总量', '综合平均能耗','能耗总量', 'GDP能耗','能源消耗水平', '能耗累计下降','产值能耗','能源消耗总量', '总产量的比例', '总量比例', '能源消费总量', '能耗分别下降', '节能量', '能源消耗年均下降', '能耗年均下降', '生产总值能耗', '能源消耗降低', '能耗降低']\n", "\n", "\n", "#Define function:\"get_keywords\" to location and Get key sentences\n", "def get_keywords(goal):\n", "    with open(path +  goal, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        Carbon_new = []\n", "        Carbon = []\n", "        Energy_c = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in C_keynew: \n", "                if i in x:\n", "                    Carbon_new.append(x)\n", "\n", "        for y in sentences:\n", "            for j in C_key: \n", "                if j in y:\n", "                    Carbon.append(y)\n", "\n", "        for z in sentences:\n", "            for k in E_key: \n", "                if k in z:\n", "                    Energy_c.append(z)\n", "\n", "        Carbon_final = Carbon_new +Energy_c +Carbon\n", "\n", "    #Drop duplicates\n", "    lists_C  = sorted(set(Carbon_final),key=Carbon_final.index)\n", "    #Drop tedious puctuations\n", "    list_C1 = '。'.join(lists_C)+'。'\n", "    C_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', list_C1)\n", "    #Keep 512 words at the beginning of the text, since sentences have been sorts by priority in Part 1-(3)\n", "    C_split = C_new[:512]\n", "    return content, goal, Carbon_final, C_split\n", "\n", "#Execute funtion and save files as .txt and .xlsx\n", "save_goal=[]\n", "save_goal1=[]\n", "for goal in path_list:\n", "    content, goal, Carbon_final, C_split = get_keywords(goal)\n", "    dic = {'filename': goal, 'C_goal': C_split}\n", "    dic1 = {C_split}\n", "    save_goal.append(dic)\n", "    save_goal1.append(dic1)\n", "\n", "df = pd.DataFrame(save_goal)\n", "df1 = pd.DataFrame(save_goal1)\n", "A = pd.concat([df], axis = 1)\n", "<PERSON>.to_excel(\"./output/City_Carbon_objective.xlsx\", index=False)\n", "df1.to_csv('./output/City_Carbon_objective.txt',sep=' ',index=0,header=0)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# -----------Extracting main texts for energy conservation objective _Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "\n", "path = './policy texts (10 policies for coding)/Policy_objective/Energy/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "#Define keywords related to energy consdervation\n", "E_keynew = ['能耗年均降低', '能耗低于', '能耗下降', '能耗降低','能耗比', '能源消耗比', '能源消耗较', '能耗小于', '能耗分别比', '综合能耗', '能源消费总量', '综合平均能耗','能耗总量', 'GDP能耗','能源消耗水平', '能耗累计下降','产值能耗','能源消耗总量', '总产量的比例', '总量比例', '能源消费总量', '能耗分别下降', '节能量', '能源消耗年均下降', '能耗年均下降', '生产总值能耗', '能源消耗降低', '能耗降低']\n", "E_key = ['差别电价','阶梯电价', '能耗', '能源消耗', '综合能耗', '能源消费总量', '综合平均能耗', '产量', '总量', '节能']\n", "\n", "\n", "#Define function:\"get_keywords\" to location and Get key sentences\n", "def get_keywords(goal):\n", "    with open(path +  goal, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        Energy_new = []\n", "        Energy_c = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in E_keynew: \n", "                if i in x:\n", "                    Energy_new.append(x)\n", "\n", "        for z in sentences:\n", "            for k in E_key:\n", "                if k in z:\n", "                    Energy_c.append(z)\n", "\n", "        Energy_final = Energy_new + Energy_c\n", "    \n", "    #Drop duplicates\n", "    lists_E  = sorted(set(Energy_final),key=Energy_final.index)\n", "    #Drop tedious puctuations\n", "    list_E1 = '。'.join(lists_E)+'。'\n", "    E_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', list_E1)\n", "    #Keep 512 words at the beginning of the text, since sentences have been sorts by priority in Part 1-(3)\n", "    E_split = E_new[:512]\n", "    return content, goal, Energy_final, E_split\n", "\n", "#Execute funtion and save files as .txt and .xlsx\n", "save_goalE=[]\n", "save_goalE1=[]\n", "for goal in path_list:\n", "    content, goal, Energy_final, E_split = get_keywords(goal)\n", "    dic_E = {'filename': goal, 'E_goal': E_split}\n", "    dic_E1 = {E_split}\n", "    save_goalE.append(dic_E)\n", "    save_goalE1.append(dic_E1)\n", "\n", "df_E = pd.DataFrame(save_goalE)\n", "df_E1 = pd.DataFrame(save_goalE1)\n", "A = pd.concat([df_E], axis = 1)\n", "<PERSON>.to_excel(\"./output/City_Energy_objective.xlsx\", index=False)\n", "df_E1.to_csv('./output/City_Energy_objective.txt',sep=' ',index=0,header=0)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# -----------Extracting main texts for capacity utilization objective _Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "\n", "path = './policy texts (10 policies for coding)/Policy_objective/CU/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "#Define keywords related to capacity utilization\n", "CU_key = ['过剩产能', '淘汰', '关停', '关闭', '淘汰落后', '去产能', '压减', '退出', '产能']\n", "\n", "\n", "#Define function:\"get_keywords\" to location and Get key sentences\n", "def get_keywords(goal):\n", "    with open(path +  goal, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        CU_new = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in CU_key: \n", "                if i in x:\n", "                    CU_new.append(x)\n", "    #Drop duplicates\n", "    lists_CU  = sorted(set(CU_new),key=CU_new.index)\n", "    #Drop tedious puctuations\n", "    list_CU1 = '。'.join(lists_CU)+'。'\n", "    CU_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', list_CU1)\n", "    #Keep 512 words at the beginning of the text, since sentences have been sorts by priority in Part 1-(3)\n", "    CU_split = CU_new[:512]\n", "    return content, goal, CU_new, CU_split\n", "\n", "#Execute funtion and save files as .txt and .xlsx\n", "save_goalCU=[]\n", "save_goalCU1=[]\n", "for goal in path_list:\n", "    content, goal, CU_new, CU_split = get_keywords(goal)\n", "    dic_CU = {'filename': goal, 'CU_goal': CU_split}\n", "    dic_CU1 = {CU_split}\n", "    save_goalCU.append(dic_CU)\n", "    save_goalCU1.append(dic_CU1)\n", "\n", "df_CU = pd.DataFrame(save_goalCU)\n", "df_CU1 = pd.DataFrame(save_goalCU1)\n", "A = pd.concat([df_CU], axis = 1)\n", "<PERSON>.to_excel(\"./output/City_CU_objective.xlsx\", index=False)\n", "df_CU1.to_csv('./output/City_CU_objective.txt',sep=' ',index=0,header=0)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# -----------Extracting main texts for technology objective _Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "\n", "path = './policy texts (10 policies for coding)/Policy_objective/Tech/'\n", "path_list=os.listdir(path)\n", "path_list.sort() \n", "\n", "#Define keywords related to technology\n", "Tech_key = ['生产', '制造', '收入', '结构', '规模', '增加值', '产值', '研究', '专利', '技术', '成果转化', '科技进步', '技术进步', '生产企业', '产业', '产品附加值', '产业链', '节能环保产业', '竞争', '先进', '容量', '自给', '效率', '技术', '研发', '创新', '产业规划', '技术改造', '装备自主', '技术产业化', '产业规模', '生产成本', '主营业务收入', '核心技术', '竞争力', '生产能力', '科技成果']\n", "#Drop duplicates\n", "def get_keywords(goal):\n", "    with open(path +  goal, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        Technology_new = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in Tech_key: #遍历列表中的各个元素\n", "                if i in x:\n", "                    Technology_new.append(x)\n", "    #Drop duplicates\n", "    lists_Tech  = sorted(set(Technology_new),key=Technology_new.index)\n", "    #Drop tedious puctuations\n", "    lists_Tech1 = '。'.join(lists_Tech)+'。'\n", "    Tech_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', lists_Tech1)\n", "    #Keep 512 words at the beginning of the text, since sentences have been sorts by priority in Part 1-(3)\n", "    Tech_split = Tech_new[:512]\n", "    return content, goal, Technology_new, Tech_split\n", "\n", "#Execute funtion and save files as .txt and .xlsx\n", "save_goalT=[]\n", "save_goalT1=[]\n", "for goal in path_list:\n", "    content, goal, Technology_new, Tech_split = get_keywords(goal)\n", "    dic_T = {'filename': goal, 'Tech_goal': Tech_split}\n", "    dicT1 = {Tech_split}\n", "    save_goalT.append(dic_T)\n", "    save_goalT1.append(dicT1)\n", "\n", "df_T = pd.DataFrame(save_goalT)\n", "df_T1 = pd.DataFrame(save_goalT1)\n", "A_T = pd.concat([df_T], axis = 1)\n", "A_T.to_excel(\"./output/City_Tech_objective.xlsx\", index=False)\n", "df_T1.to_csv('./output/City_Tech_objective.txt',sep=' ',index=0,header=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 3.3 Extract main texts of instruments for manual labelling (<=512 words)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["一、淘汰对象（一）中天钢铁集团有限公司现有落后炼铁工艺装备为250立方米高炉1座中天钢铁集团有限公司现有落后炼铁工艺装备为250立方米高炉1座。一、淘汰对象（二）常州市新华钢铁有限公司现有落后炼钢工艺装备为20吨电炉2座常州市新华钢铁有限公司现有落后炼钢工艺装备为20吨电炉2座。根据淘汰工作要求，结合实际情况，制订鼓励淘汰企业加快实施淘汰工作的相关具体政策措施。从2007年7月起，对淘汰企业实施差别电价等限制措施，中天钢铁250立方米高炉单独装表计量，新华钢铁全厂实行差别电价。五、淘汰工作措施淘汰工作措施,。五、淘汰工作措施（一）统一认识，加强领导统一认识，加强领导。五、淘汰工作措施（二）明确责任，形成合力明确责任，形成合力。根据上级部署和安排，结合我市实际情况，制定和认真落实实施支持、促进淘汰落后产能的政策措施。五、淘汰工作措施（三）细化方案，加强督查细化方案，加强督查。对按规定淘汰落后产能、拆除落后装备的企业，及时停征差别电价，取消限制措施。\n", "2.把好新建项目的审批关，制定符合生态市要求的控制高能耗、高水耗、重污染建设项目市场准入的政策措施，凡是不符合产业政策、污染严重、能耗大的建设项目一律不予立项审批。2.建立正常、有效的活水机制，视市区河道水位、水质情况，适时进行调水，改善市区河道水环境。6.加强市政工地管理，实施工地现场洒水。2.指导建设大中小型沼气工程8座、2个沼气村，开展沼气集中供气试点。十六、市交通局市交通局1.严格执行交通建设项目环境影响评价制度，交通建设项目生态恢复率保持100%。3.在国家4A级景区的旅游区全面开展ISO14000环境管理体系认证，并引导3A级景区在向4A级景区提升过程中开展ISO14000环境管理体系认证。\n", "突出抓好冶金、电力、化工、建材、纺织等重点耗能行业和年耗能5000吨标准煤以上重点耗能企业的节能工作。在此基础上，要把国家和省的节能工作要求贯彻到全市年耗3000吨标准煤以上的重点耗能企业，明确要求，加强监督考核。认真落实《省政府办公厅关于转发省经贸委等部门关于进一步推进墙体材料革新和实施建筑节能意见的通知》（苏政办发〔2005〕130号）、市政府《关于进一步推进墙体材料革新和推广建筑节能的实施意见》（常政发〔2006〕86号）等文件精神，大力发展新型、节能型墙体材料。加快实施产业发展“四四三”战略和企业发展“8880”工程，鼓励发展高新技术产业，壮大优势产业，改造、提升传统产业，积极推进企业集群、产业集聚，严禁高耗能、高污染项目上马，严格控制高耗能企业低水平扩张，加快淘汰小冶金、小水泥、小化工、小印染等五小企业，通过更高层次、更高水平的优化，推进全市五大高耗能行业的产品结构调整。各地区、各有关部门和单位要抓紧落实相关政策措施，确保节能项目配套资金到位。3.加强重点耗能企业节能管理。加快出台对全市重点耗能企业的有关节能管理考核办法，重点要求年耗能3000吨标准煤以上的企业进一步强化节能管理，按规定设立能源管理岗位，配备人员，健全节能管理制度和能源计量管理体系，按照《用能单位能源计量器具配备和管理通则》（GB17167-2006）的要求配备计量器具，完善计量器具台帐，并定期进行检定校准，确保能源计量数据准确。二、重点方向及主要措施（六）强化节能管理队伍建设和基础工作强化节能管理队伍建设和基础工作1.加强节能管理队伍建设。各级政府要加强节能管理队伍建设，充实节能管理力量，完善节能监督体系，确保经费支出，强化对本行政区域内节能工作的监督管理和日常监察（监测）工作。各辖市、区政府要对本地区节能工作负总责，把节能工作纳入政府重要议事日程，主要领导要亲自抓，并建立相应的协调机制，明确相关部门的责任和分工，确保责任到位、措施到位、投入到位。\n", "\n"]}], "source": ["# -----------Extracting main texts for command-control instrument_Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "import nltk\n", "import jieba\n", "\n", "path1 = './policy texts (10 policies for coding)/Policy_instrument/CC/'\n", "path_list=os.listdir(path1)\n", "path_list.sort() \n", "\n", "#Define keyeords for policy instruments\n", "keywords = ['停业整顿', '关停', '关闭', '问责', '追究', '责任', '处分', '整改', '罚款', '处罚', '注销', '吊销', '准入', \\\n", "               '禁止', '新上', '进入', '落实', '强化', '推动', '巩固', '置换', '替代', '淘汰', '兼并', '重组', '压减', '强制公开', \\\n", "                '负面清单', '核准', '支持', '价格措施', '制度', '细则', '扩大', '体系', '试点', '核查', '管理', '分配', '机制', \\\n", "                '履约', '去产能', '严查', '倒逼', '上大压小', '任务分解', '标准', '资金', '技术改造', '投资', '预算', '财政', '奖励', '贷款', \\\n", "                '贴息', '金融', '融资', '债券', '基金', '担保', '保险', '资本', '股权', '信贷', '价格措施', '差别电价', '阶梯电价', '财税', \\\n", "                '投入', '税收', '补贴', '免税', '风险补偿', '债务融资', '减免', '优惠', '碳排放权交易', '碳市场', '配额', '碳排放报告', '碳排放核查']\n", "\n", "\n", "#Define function:\"get_keywords\" to locate and Get key sentences\n", "def get_keywords(instrument):\n", "    with open(path1 +  instrument, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        Instrument = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in keywords:\n", "                if i in x:\n", "                    Instrument.append(x)\n", "    #Drop duplicates\n", "    lists_instrument  = sorted(set(Instrument),key=Instrument.index)\n", "    #Drop tedious puctuations\n", "    list_instrument1 = '。'.join(lists_instrument)+'。'\n", "    instrument_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', list_instrument1)\n", "    return instrument, instrument_new, lists_instrument\n", "\n", "#Define function:\"sent_tokenizer\" to split sentences\n", "def sent_tokenizer(instrument_new):\n", "    content = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', instrument_new)\n", "    start=0\n", "    i=0\n", "    sentences=[]\n", "    punt_list='!?。！？'\n", "\n", "    for text in content:\n", "        token=content[start:i+2]\n", "        if text in punt_list and token not in punt_list: \n", "            sentences.append(content[start:i+1])\n", "            start=i+1\n", "            i+=1\n", "        else:\n", "            i+=1\n", "            token=list(token).pop()\n", "    if start<len(content):\n", "        sentences.append(content[start:])\n", "    return sentences\n", "\n", "#Define function\"stopwordslist\" to load storwords\n", "def stopwordslist(filepath):\n", "    stopwords = [line.strip() for line in open(filepath, 'r', encoding='utf-8').readlines()]\n", "    return stopwords\n", "\n", "#Get high-frequency phrases\n", "save_instrument=[]\n", "save_instrument1=[]\n", "for Instrument in path_list:\n", "    Guo_instrument, instrument_new, lists_instrument= get_keywords(Instrument)\n", "    num1 = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '措施', '审批', '核准', '备案', '监察']\n", "    c_new1 = []\n", "    for x in lists_instrument:\n", "        for i in num1: \n", "            if i in x:\n", "                c_new1.append(x)\n", "    #Drop duplicates\n", "    c_new2  = sorted(set(c_new1),key=c_new1.index)\n", "    c_new22 = '。'.join(c_new2)+'。'\n", "    c_new3 = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', c_new22)\n", "\n", "    stopwords = stopwordslist('Part 3.3 Stopwords.txt')\n", "    #Split sentences\n", "    sentence=sent_tokenizer(c_new3)\n", "    words=[w for sentence in sentence for w in jieba.cut(sentence) if w not in stopwords if len(w)>1 and w!='\\t']#词语，非单词词，同时非符号\n", "    #Calculate frequency\n", "    wordfre=nltk.FreqDist(words)\n", "    #Get top 50 phrases with highest frequency\n", "    topn_words=[w[0] for w in sorted(wordfre.items(),key=lambda d:d[1],reverse=True)][:50]\n", "\n", "    #Define the function\"_score_sentences\" to Score sentences\n", "    def _score_sentences(sentences,topn_words):#sentences：sentences after splicting; topn_words：top 50 phrases with high-frequency\n", "        scores=[]\n", "        sentence_idx=-1 #mark the 1st senctnce\n", "        for s in [list(jieba.cut(s)) for s in sentences]:# traverse all sentences\n", "            sentence_idx+=1 \n", "            word_idx=[]#save the position of high-frequency phrases in sentences\n", "            for w in topn_words:\n", "                try:\n", "                    word_idx.append(s.index(w))\n", "                except ValueError:#sentences without high-frequency phrases\n", "                    pass\n", "            word_idx.sort()\n", "            if len(word_idx)==0:\n", "                continue\n", "\n", "            #Define cluster based on the distance between positions between high-frequency phrases in the sentence\n", "            clusters=[] \n", "            cluster=[word_idx[0]] \n", "            i=1\n", "            while i<len(word_idx):\n", "                CLUSTER_THRESHOLD=2\n", "                if word_idx[i]-word_idx[i-1]<CLUSTER_THRESHOLD: #belongs to same cluster if the distance is less than the threshold\n", "                    cluster.append(word_idx[i])\n", "                else:\n", "                    clusters.append(cluster[:])#other cluster\n", "                    cluster=[word_idx[i]] \n", "                i+=1\n", "            clusters.append(cluster)\n", "            \n", "            #Score each cluster\n", "            max_cluster_score=0\n", "            for c in clusters:\n", "                significant_words_in_cluster=len(c)\n", "                total_words_in_cluster=c[-1]-c[0]+1\n", "                score=1.0*significant_words_in_cluster*significant_words_in_cluster/total_words_in_cluster\n", "                if score>max_cluster_score:\n", "                    max_cluster_score=score\n", "            scores.append((sentence_idx,max_cluster_score))\n", "        return scores;\n", "    scored_sentences=_score_sentences(sentence,topn_words)\n", "\n", "    #Sort sentences with score in top 20 \n", "    top_n_scored=sorted(scored_sentences,key=lambda s:s[1])[-10:]\n", "    top_n_scored=sorted(top_n_scored,key=lambda s:s[0])\n", "    c= dict(top_n_summary=[sentence[idx] for (idx,score) in top_n_scored])\n", "    c_new111 = ''.join(c['top_n_summary'])+''\n", "    c_new333 = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', c_new111)\n", "    print(c_new333)\n", "\n", "    #Keep 512 words in the medium of the text, if texts are longer than 512\n", "    cc = int(len(c_new333))/2\n", "    cnew_split = c_new333[int(cc-256):int(cc+256)]\n", "    if len(c_new333)<=512:\n", "        instrument_key = c_new333\n", "    else:\n", "        instrument_key = cnew_split\n", "    dic_a = {'filename': Guo_instrument, 'instrument': instrument_key}\n", "    dic_a1 = {instrument_key}\n", "    save_instrument.append(dic_a)\n", "    save_instrument1.append(dic_a1)\n", "\n", "df_a = pd.DataFrame(save_instrument)\n", "df_a1 = pd.DataFrame(save_instrument1)\n", "A = pd.concat([df_a], axis = 1)\n", "<PERSON>.to_excel(\"./output/City_CC_instrument.xlsx\", index=False)\n", "df_a1.to_csv('./output/City_CC_instrument.txt',sep=' ',index=0,header=0)\n", "\n", "#Part of this code refers to https://blog.csdn.net/kobeyu652453/article/details/115162219"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2010年国家科技部批准我市启动建设国家创新型试点城市和国家创新型科技园区。3．企业创新能力不断提升大力实施创新型企业和高新技术企业培育工程，引导企业加大科技创新投入，加强技术与产品创新，提高创新产出水平，企业的技术创新能力明显提升。\"四、“十二五\"\"科技发展保障措施“十二五\"\"科技发展保障措施1．加强组织领导强化“科学技术是第一生产力\"\"和“人才是第一资源\"\"的意识，坚持“一把手抓第一生产力，一把手抓第一资源\"\"的工作方针，把科技创新作为推动国民经济和社会发展的主要动力，把建设创新型城市、发展创新型经济作为“十二五\"\"期间党委和政府的中心工作，统一认识，科学谋划，精心组织，提高对科技发展规划、重点工作部署以及重大创新政策的执行能力。6．加强绩效管理加强对各级领导干部在科技创新投入、科技创新产出、科技创新环境改善等方面的绩效考核，促进各级领导干部的工作认识、工作措施与工作成效“三到位\"\"。培育重点产品群：纯电动微型汽车产品群（含整车、新型动力电池及关键材料、驱动电机、电控等），成为国内重要的纯电动汽车研发及生产基地；机器人及智能装备产品群（包括电子电声行业用成套机器人、LED行业用成套机器人、娱乐机器人、特种机器人、老年服务机器人、自动化成套装备等），成为国内重要的机器人及智能装备研发及孵化基地；以互联网、物联网为基础的软件和信息服务业、服务外包业、文化创意产业，优先发展电子商务、基于3D/4D技术的动漫产品；生物医药产品群，包括新型药物研发、新型试剂研发、新型医疗器械研发；新材料产品群，包括电子材料、LED材料、医用材料等；金融业、科技服务业、商务服务业等。重点项目：800kV以上交直流输变电设备关键技术、电网和系统保护技术、无功补偿技术、电网就地调峰技术、铝包殷钢芯耐热铝合金倍容量导线技术开发及产业化；低压智能配电系统和元件关键技术开发及规模产业化；超高压和特高压直流控制保护系统开发及应用；大容量超导输电电缆设计与制造技术；输电线路运行状态监测与故障诊断技术；智能配电网监控与控制软件平台技术；高精度、长寿命、低功耗的新型智能电表研发及产业化；用户电能使用优化管理软件平台；光伏、风力发电等分布式新能源电力并网技术。\"一、培育新兴产业（二）新医药产业新医药产业主题6：生物技术药与试剂重点发展以治疗性抗体为代表的靶向性治疗药物，开发形成一批针对肿瘤、心血管疾病、病毒感染、神经系统疾病和自身免疫性疾病等重大疾病的基因工程蛋白药物及核苷酸药物等化学药替代新药；大力发展以重组疫苗为重点的新型疫苗，并开发联合疫苗应用技术；基因治疗药物输送系统及靶向药物载体技术；支持以蛋白质结构为基础的新医药筛选和开发；改造和提升多肽类药物制备工艺技术；积极突破干细胞应用技术等一系列生物治疗技术；核酸干扰技术；重点开发面向肝炎、人禽流感等传染病和肿瘤早期诊断试剂和检测技术；积极扶持肝素等生物药物制剂产品扩大市场占有率。主题8：医疗设备及医用材料重点开发具有生物活性的介入式医疗器械及数字化医疗设备，人体硬组织修复与替代、血液透析器及滤过器、血液透析浓缩液、气动管道物流传输系统股骨近端手术器、肛直肠肌功能生物反馈仪、精密蠕动灌装系统、血液透析器和滤过器等方面。\"四、改造传统产业改造传统产业主题25：制造业信息化面向装备制造业，支持企业围绕产品设计、分析、生产、装配等需求，开展基于高端三维产品的数字化设计、工艺、仿真、分析、数据安全、制造的集成应用，建立数字化设计、制造集成平台，建立以“甩图纸\"\"为特征的产品设计与生产制造信息集成示范应用。\"五、提升现代农业提升现代农业主题27：新品种选育及引试针对我市农业良种覆盖率不高、品种附加值低和品种多乱杂的实际情况，运用基因重组、细胞融合以及组织培养等现代生物育种技术，结合常规育种技术，在功能性品种开发、品质提升、抗病虫害、抗逆等方面取得突破，开展良种选育工作，提高主栽农作物品种的产量、品质以及抗逆抗病能力。\n", "为保障低碳试点城市建设工作的顺利进行，未来苏州市需实施“12345”低碳发展整体思路，即：一项约束——以碳强度下降率为约束；两大体系——构建减缓温室气体排放和适应气候变化两大体系；三个主体——政府、企业、公众三大主体同时发力；四大重点——以加快产业低碳化发展、引导绿色低碳消费、转变能源利用方式、增强城市碳汇能力为重点；以及五项支撑——以体制机制改革、低碳制度创新、低碳能力建设、低碳技术应用、低碳试点示范为支撑，全面推进低碳城市建设工作，提升全社会应对气候变化的能力。图3苏州市低碳发展规划体系图3．指标体系。为衡量苏州市低碳发展水平，本规划将低碳发展指标分为两大类：约束性指标和非约束性指标，其中约束性指标是衡量苏州市低碳发展最直接、且必须完成的指标，即温室气体排放强度和人均温室气体排放；非约束性指标用于支撑约束性指标的实现，分别从与低碳发展密切相关的产业发展水平、能源发展方式、消费层面低碳化水平、碳汇能力及保障体系五方面提出，指标体系如表2所示。1．建设温室气体排放数据管理平台。附件：1．苏州市低碳发展重点行动2．情景分析中主要参数与特征设定一览表3．低碳发展指标体系解释附件1苏州市低碳发展重点行动行动类别序号工程名称实施内容项目目标主要责任单位2015年2020年加快产业低碳化发展产业结构优化行动1现代服务业提升工程以国家级服务外包示范基地和中国金融BPO示范区为抓手，加快发展软件开发、生物医药、检测认证、研发设计、动漫创意、供应链管理、金融后台等服务外包业态，积极培育大型外服务包企业，实现生产性服务业跨越式发展；提升旅游、商贸、文化等消费性服务业的发展规模和水平，加强城乡旅游服务设施建设，巩固扩大苏州水乡、古城的品牌效应，培育消费性服务业；创新发展金融业，加快发展总部经济，突破发展会展业服务业占GDP比重达48%服务业占GDP比重达60%市发改委、金融办、商务局、科技局、经信委、园林和绿化局、旅游局2新兴产业腾飞工程加快战略新兴产业发展，实现到2015年战略性新兴产业产值占规模以上工业总产值比重达50%，培育30个以上市级新兴产业基地；到2020年战略性新兴产业产值占规模以上工业总产值比重达55%减排400万吨CO2减排1500万吨CO2市经信委、发改委、科技局工业体系转型升级行动3落后产能淘汰工程逐步关停高能耗、低产值企业，淘汰列入产业结构调整指导目录、各地出台的产业政策中处于淘汰类别的生产工艺和设备；淘汰符合产业政策但经限期治理难以稳定达标的项目减排1000万吨CO2减排3500万吨CO2市经信委、发改委、环保局4能源审计范围扩大工程到2020年将5000吨标准煤重点耗能企业能源审计范围扩展至年耗能3000吨标准煤以上的企业，扩大能源审计范围，指导督促企业采取综合性措施，挖掘节能潜力减排400万吨CO2减排1000万吨CO2市经信委、发改委、统计局工业体系转型升级行动5行业节能改造工程在电力、钢铁、石化及化工、建材等高耗能行业，大力组织实施锅炉（窑炉）、电机系统、余热余压利用、能量系统优化等节能改造；组织实施节能关键和共性技术攻关，推动节能技术产业化应用示范减排500万吨CO2减排1700万吨CO2市经信委、科技局、发改委、环保局6数字化能源管理工程推进企业能源管理中心建设，以钢铁、建材、化工等流程工业为重点，采用自动化、信息化和集中管理模式，对企业能源系统的生产、输配和消耗环节实施集中扁平化的动态监控和数字化管理市经信委、发改委7能源管理平台服务工程进一步完善能源计量检测与能效评估服务平台，建立覆盖全市重点耗能单位的能源计量数据中心，开展能源计量数据在线采集、动态监控，逐步扩大能耗监控范围建立苏州能源计量数据中心，实现50家重点耗能单位数据在线采集实现苏州300余家重点用能单位能源计量数据在线采集苏州质监局，市经信委、发改委工业过程减排行动8钢铁产业链延伸工程限制粗钢产量本地增长，大力发展钢铁大物流；推动废钢回收，鼓励采用以废钢为原料的短流程工艺；减排230万吨CO2减排600万吨CO2市经信委9氟化工产业升级与减碳工程合理控制氟化工企业盲目发展和重复建设，加快提高行业内生产技术水平，调整氟化工产品结构，重点发展高质量、高附加值含氟精细化学品，延伸产业链，加速淘汰落后产能，加大尾气处理力度市经信委、发改委、环保局10电子电器工艺优化工程通过改造工艺、采取控排技术，减少工业过程中的氢氟碳化物、全氟化碳和六氟化硫等温室气体排放市环保局、发改委11水泥行业减碳工程在水泥生产中，进一步减少熟料用量，大力推动粉煤灰、矿渣、脱硫石膏等工业固体废渣和非碳酸盐原材料替代石灰石原料，加快发展硫铝酸盐水泥、高标号水泥等特种水泥和新型低碳水泥市经信委转变能源发展方式能源清洁利用行动12天然气推广工程建设西气东输二线工程嘉兴——甪直联络线、“川气东送”苏州配套管线、天然气高压管网三期工程；建设2个各5000m3的天然气储配站，2015年供气量达60亿m3，2020年达100亿m3减排300万吨CO2减排800万吨CO2市发改委、住建局13太阳能光伏应用广工程建设光伏发电示范项目，推广太阳能热水器使用；“十二五”末，新增太阳能光伏建筑应用面积120万平方米，实现光伏发电装机容量30万千瓦，2020年光伏发电装机容量100万千瓦减排56万吨CO2减排130万吨CO2市发改委、住建局14阳澄湖风能项目加快阳澄湖现代农业产业园内风力发电厂的建设，到2015年实现风电装机容量9.9万千瓦，2020年风电装机容量达到15万千瓦市发改委、经信委15地源热泵推广项目推广地源热泵技术应用，加大资金补贴与技术扶持，2015年浅层地热能（包括水源、地源及污水源等余热资源）建筑应用面积达到700万平方米，2020年达到1500万平方米市住建局、发改委、财政局16生物质能示范项目推广垃圾发电及填埋气发电项目，扩建两个焚烧厂，新建苏州市垃圾焚烧发电厂三期工程、吴江生活垃圾焚烧发电项目；加快秸秆集中气化应用市市容市政局、农委，吴江区政府17分布式能源系统建设分阶段开展分布式能源系统建设，力争“十二五”末形成一批有示范效应的分布式能源利用系统项目市发改委、经信委18智能电网建设工程坚持先试点、后推进，开展智能电网建设工作，提高配网配电效率，提高供用双方智能互动能力市发改委、经信委引导绿色低碳消费低碳交通体系构建行动19综合运输网络优化工程加大投资力度，推进交通基础设施建设，促进交通运输结构优化调整，形成立体化交通网络体系市交通运输局、住建局、规划局、发改委20城市轨道交通建设项目推进轨道交通建设，到“十二五”末，建成2号线、上海轨道交通11号线花桥延伸段，部分建成苏州轨道4号线（含支线）减排400万吨CO2减排800万吨CO2苏州轨道集团，市发改委低碳交通体系构建行动21公共自行车系统项目2015年，全市六区公共自行车网点规模达1500个，投放公共自行车约30000辆；2020年建成区基本实现公共自行车网点全覆盖市市容市政局，各市、区政府22新能源汽车试点工程试点新能源公共汽车、混合动力公交车、电动保安车、电动环卫车等节能型公交工具，至2015年，实现全市1000辆新能源汽车目标；到2020年，实现全市1500辆新能源汽车目标市科技局、交通运输局、财政局、发改委、经信委低碳建筑推广行动23绿色建筑推进工程扩大对绿色建筑设计、建造和运营的政府补贴力度市住建局、国土局、规划局、发改委24新建建筑节能工程加强新建建筑节能全过程监管，推行建筑节能65%设计标准到2015年，市区新建公建全面执行节能65%标准，全市城镇新建民用建筑全面按一星及以上绿色建筑标准设计建造2020年全市50%的城镇新建民用建筑按二星及以上绿色建筑标准设计建造市住建局、国土局、规划局、发改委25既有建筑节能改造工程以国家机关办公建筑和大型公共建筑节能改造为重点，结合老城改造、小区出新，同步推进既有住宅节能改造，“十二五”完成改造170万m2减排100万吨CO2减排150万吨CO2市住建局、市级机关事务管理局26新能源建筑应用示范因地制宜选择土壤源热泵、污水源热泵、热电冷联供等技术模式，推广新能源建筑应用2015年，应用面积超2000万m22020年，应用面积达到5000万m2市住建局、发改委低碳社区创建行动27低碳宣教活动在社区和学校开展低碳生活、低碳出行和低碳消费方面的宣传教育活动2015年，公众低碳发展认知度达70%2020年，公众低碳发展认知度达100%市委宣传部，市政府办公室、发改委、环保局、教育局低碳社区创建行动28低碳示范创建工作鼓励社区、学校等开展低碳创建工作，申报市级低碳工作试点示范单位，激励基层单位参与低碳城市建设2015年，全市范围打造15个低碳社区，15个低碳学校2020年，全市范围打造30个低碳社区，25个低碳学校市政府办公室、发改委、教育局、民政局29城市生活垃圾资源化利用项目优化生活垃圾收运体系，增配市容环卫专用车辆设备，建设再生资源分拣回收工厂，优化生活垃圾分类回收网络，出台苏州市垃圾分类政府规章及配套规范性文件；加大教育宣传力度，扩大垃圾分类试点范围新增垃圾分类试点小区200个新增垃圾分类试点小区300个市市容市政局、环保局、供销总社增强城市碳汇能力碳汇能力建设行动30森林生态资源保护工程全面启动生态片林建设工程和森林质量提升工程，加快实施宕口复绿工程，加快山体复绿，到2015年森林蓄积量增加到206万立方米以上，2020年森林蓄积量增加到230万立方米以上2015年较2010年增加碳汇100万吨2020年较2010年增加碳汇190万吨市农委、国土局，各市、区政府31湿地保护工程在太湖、阳澄湖、长江等重要湿地区域划定湿地保护小区，认定市级以上的重要湿地100个；2015年末，自然湿地保护率达到45%市农委、园林和绿化局32生态园林工程实施河湖林网构建、绿色通道提档、村镇环境美化、果茶苗木增效等工程，建设250项市级农村绿化重点工程、200个绿化示范村，2015年末新增林地、绿地30万亩，林木覆盖率达19.3%，陆地森林覆盖率达到29%；重点完成石湖生态园建设工程、虎丘山风景名胜区环境综合整治和建设项目、推进虎丘湿地公园续建工程；每年新增改造绿地500万平方米以上，到2015年，苏州市区及4县市城关镇建成区绿地率达到38%，绿化覆盖率达到43%；乡镇建成区绿地率≧31%，绿化覆盖率≧36%市园林和绿化局、农委低碳发展保障体系低碳制度创新行动33碳排放测评工程由政府提供交流对接平台，推动企事业单位与第三方机构签订碳排放测评项目苏州质监局，市发改委、经信委、统计局34碳排放交易平台建设借鉴国外发达城市的碳排放交易体制，以碳排放核查结果为基础，探索推行碳排放配额制度，试行自愿减排协议，通过碳排放交易平台建设全面推动碳排放交易工作市发改委、统计局、财政局35低碳产品认证工作在首批碳标签农产品“田娘”、“苏阿姨”基础上构建低碳产品认证体系苏州质监局，市农委、发改委、经信委36低碳发展绩效评估试点工作完善评估指标体系，建立长效反馈机制，结合能源碳排放核查、监管与评估机制的推进，实现绩效评估标准化，建立健全地方政府低碳绩效评估制度市政府办公室、发改委低碳能力建设行动37碳排放数据管理平台建立碳排放数据管理平台，并实施水、电、气、煤等的实时监控，为我市碳排放管理提供数据基础市发改委、统计局38低碳发展方案编制各区县政府需编制本地低碳发展方案，且需得到上一级发改部门的认可市发改委39低碳决策支持团队建设建立专家咨询顾问团队和不同领域的专家团队市发改委40低碳发展资金投入完善财政资金投入机制，加大低碳发展财政扶持力度，在现有的“节能专项资金”和“污染防治专项资金”基础上，按照《苏州市市级财政专项资金管理办法的通知》中的相关规定，尽快设立“低碳发展专项资金”，各级财政将“低碳发展专项资金”纳入年度预算，经常性预算中优先保障相关预算，确定支出额度和增长幅度，稳步提高低碳发展投资占预算总投资的比重“十二五”期间“低碳发展专项资金”投入不低于2000万元“十三五”期间低碳发展专项资金”投入不低于3000～5000万元市财政局、发改委41低碳统计制度完善建立完整的数据收集核算体系，组织编制温室气体排放清单，并探索建立县级市等基层层面低碳管理制度及统计方法市统计局、发改委、经信委低碳试点示范行动42低碳示范园区建设推进苏州工业园区、昆山高新区两个全省首批低碳经济试点园区建设，在空间布局、产业、能源、建筑、交通、社会等各个领域发展低碳经济；进一步推动园区申报国家低碳园区，鼓励其它经济开发区开展不同层次的低碳园区试点2015年累计打造不同层次的低碳园区试点5个2020年累计打造不同层次的低碳园区试点10个市发改委，各市、区政府43低碳循环型物流园改造试点选择张家港保税物流园区、太仓港物流园区、苏州工业园区现代物流园、苏州高新区综合保税区四个物流集聚区开展低碳循环型物流园区的改造示范建设市发改委，各市、区政府44中新生态科技城低碳综合示范区建设加快建设中新生态科技城基础设施，实现绿色低碳建筑全覆盖示范区新建建筑中绿建比例为100%苏州工业园区管委会45低碳企业试点开展“万企节能低碳行动”，深化对能耗大的重点企业的低碳示范改造；实施低碳企业示范工程，推进省低碳经济试点企业建设，推动全市列入“万企节能低碳行动”的用能单位全面建设能源管理体系304家用能单位能源管理的建立和运行效果通过国家认证认可监督管理委员会批准资质的机构认证或节能主管部门组织的效果评价市经信委，苏州质监局、市财政局、教育局、交通运输局46低碳建筑示范区建设在苏州国际科技园、苏州大学科技创业园、苏州软件园等各类生产性服务业集聚区推进建筑节能和绿色建筑示范区的建设建成3个省级建筑节能和绿色建筑示范区市住建局、发改委47低碳社区试点在苏州各区县开展低碳社区试点创建；开展低碳家庭创建活动，引导更多家庭参与节能减排，倡导低碳生活市政府办公室、发改委、民政局提升气候变化适应能力农林业48生态农业区建设工程建设环太湖生态农业圈有机农业工程，实施有机农业建设工程；推进有机产品认证，转换种植业栽培方式组织生产；建设循环农业工程建设，建设种养结合、立体种养等循环农业工程全市建设循环农业工程20处全市建设循环农业工程50处市农委、财政局，苏州质监局49高标准农田建设工程开展实施高标准农田建设，制定相应的建设方案，建设面积等新增高标准农田25亩新增高标准农田50亩市农委50节水农业建设工程开展高效农业水肥一体化建设，重点开展灌溉施肥系统建设，配备输水系统的控制枢纽、输配水管道、灌水器、施肥装置，开展可溶性肥料研制和产业化开发以及相关高效节水和施肥技术研发、推广等市农委51避灾农业技术推广工程开展设施园艺建设，创建设施作物标准园，建设以保（增）温、避雨、防虫、节水、节肥、节药为主农业基础设施，建设工厂化育苗中心、产后商品化处理中心设施；支持畜禽圈舍、养殖设施、畜禽规模养殖场废弃物综合利用和防疫设施建设创建省级以上蔬菜标准园20个创建省级以上蔬菜标准园25个市农委52农作物育种工程选育高抗逆性（抗旱、抗涝、抗高温、低温等）果树、花卉品种、蔬菜新品种和棉花抗涝渍新品种育种工程2个育种工程5个市农委农林业53农村水利工程以小型农田水利重点县建设为抓手，重点实施小型灌排泵站、灌区渠系配套、高效节水灌溉工程、山丘区塘坝、小型涵闸等小型农田水利建设市水利局54林业资源保护工程通过实施林地林木与生态公益林保护、森林消防、湿地保护与恢复、野生动植物保护、自然保护区建设、林业有害生物防控6项子工程，切实提高森林资源管理和保护的能力和水平市农委水资源55长江河道整治支流治理开展长江堤防工程，干流河势整治工程市水利局56城市防洪工程在全市续建城市排水工程，提高防洪堤标准，形成城市防洪排涝体系市水利局卫生健康57环境相关疾病预防工程针对季节、极端天气等引起的相关疾病开展资料调研；开展应对突发急性传染病的物资和技术储备市卫生局58公共卫生设施建设工程增加政府对城市卫生社区、乡镇卫生院、村卫生室等公共卫生系统投入，提高公共卫生硬件设施水平市卫生局防灾救灾59气象灾害监测预警服务基础性工程主要建设立体、连续的数字大气综合监测网、数据分析处理系统、高性能数值预报计算中心、气象灾害预警信息发布系统以及信息网络支撑系统等，提高气象灾害监测预警服务能力市气象局60防灾救灾组织宣传工程在城市社区、镇、村定期开展气象防灾救灾科普宣传，组织专门人员定期发放各类灾害预防手册等市气象局附件2情景分析中主要参数与特征设定一览表参数基准情景产业结构主导情景技术主导情景综合减排情景GDP2011年～2015年年均增长速度为10.42%，2016～2020年为7.40%，2021～2025年为6%，2026～2030年为5%人口2015年常住人口控制在1100万左右，2015～2020年年均增长0.92%，2020～2025年年均增长0.54%，2026～2030年年均增长0.30%能源结构全市一次能源消费总量中，煤炭比重稳步下降，油气比重不断提升，增加太阳能光伏、风电等新能源的供给。12．市级低碳试点示范社区个数指标解释：针对全市范围内的各个小区，参照苏州市第一批市级低碳工作试点示范申报、评选办法，评定达到标准的低碳社区个数。计算公式如下：目标年数据参照《市政府办公室关于公布苏州市低碳工作试点示范单位名单的通知》（苏府办〔2013〕4号），规划每五年增加15个市级低碳试点示范社区。14．温室气体排放数据管理平台指标解释：定性描述苏州市温室气体排放数据管理平台的建设情况，温室气体排放数据管理平台作为科学低碳管理的基础，应将其纳入苏州市低碳制度评估指标体系。15．低碳认证制度指标解释：定性描述苏州市低碳认证体系的发展情况，反映出社会生产和消费环节在低碳城市建设工作中的参与程度。19．低碳发展绩效评估机制指标解释：主要用以评估政府低碳试点城市建设工作的开展水平与取得成就。\n", "二、科技创新科技创新7全社会研发投入占地区生产总值比重%预期性3左右8百万人口发明专利授权数件预期性5009科技进步贡献率%预期性60以上10人才资源总量万人预期性100,。五、总量预期总量预期29地区生产总值亿元预期性5500以上30人均地区生产总值（常住人口）万元预期性10以上31地方一般预算收入亿元预期性500以上32城市化率%预期性70以上33全社会固定资产投资亿元预期性累计1000034实际利用外资亿美元预期性累计150第三篇开创发展新篇章,。第五章建设创新型城市完善创新制度和体系，到2015年，基本建成以产业创新为主，科技创新、文化创新、服务创新相结合的综合性、开放式的创新型城市，创新型企业和具有自主知识产权的核心技术及产品大量涌现，公众基本科学素养大幅度提高。鼓励企业加大创新投入，到2015年企业创新研发年投入达到300亿元，形成95%以上的研发机构、90%以上的研发人员、90%以上的研发资金、90%以上的发明专利来自企业的良好创新局面。深入实施新一轮千名海外人才集聚工程、领军型创新创业人才工程和科教城千百菁英计划，力争五年累计引进500个海归创业团队，引进5000名海内外创新创业人才，形成5万名创投风投、中介服务和创意产业等现代服务业人才，累计达到50万名先进制造业和现代服务业的管理、专业技术人才总量，争取引入更多的列入国家“千人计划”的顶尖人才和创新项目，更多的创新创业人才和团体纳入省“双创人才”和重点支持的创新团队。到2020年，全市分层次、有计划、大规模引进并重点支持1万名高层次创新创业人才。加快发展金融服务业，建立完善区域金融服务体系，积极提升创业投资，加快发展产业投资基金，有效推进各类产业金融发展，新增银行类金融机构5家，新增农村小额贷款公司和科技小额贷款公司各20家，培育形成上市企业60家。推进节能降耗，加大冶金、建材、化工、电力等行业节能改造，对年耗能3000吨标准煤以上企业进行重点用能管理，完善项目建设节能评估和审查。加强养老服务机构建设，鼓励社会力量发展多层次养老服务，以公建民营、民办公助、购买服务等形式吸引民间资本投资养老服务，到2015年养老机构床位数达到老年人口总数的3.2%。五年累计固定资产投资1万亿元以上。\n", "一是对各类投资主体研制开发新兴产业项目实行鼓励政策，加快项目的核准、备案。二是从五大产业发展资金中安排5000万元专项资金用以引导各类金融机构支持新兴产业发展。2．发挥五大产业和创新型科技园区建设专项资金的作用。从五大产业发展资金中安排5000万元专项资金用以引导各类金融机构支持新兴产业发展。5．强化新兴产业发展的服务支撑体系。2．加快发展产业投资。3．创新融资担保方式。发挥我市高等职业教育资源优势，调整优化高校学科布局，强化新兴产业领域专业建设，构建面向新兴产业的学科体系，三年内我市高校新增专业70%以上要与新兴产业有关。牵头部门发挥牵头协调作用，提出年度工作计划，明确工作目标和任务，采取扎实有效的工作措施推进落实。2．强化规划引导功能。\n", "巩固发展手扶拖拉机在国内同行业的优势地位，进一步扩大中型拖拉机和直联传动小型拖拉机的产能，加快开发科技含量高、可靠性强、操作方便的130-280大马力拖拉机及其配套机具。风电装备重点突破2-3兆瓦海上直驱式风力发电机组整机制造瓶颈，扩大风力发电专用齿轮箱、轴承等关键零部件的生产能力，实现风电设备生产的完整化、产业化、规模化。五、主要措施（一）创新机制体制创新机制体制按照市场化运作、开放性重组的思路，支持装备制造业企业创新体制机制，形成开放发展新格局。五、主要措施（四）培育龙头企业培育龙头企业围绕产业链，编制装备制造业重点企业名录和重大技术装备名录，积极推荐重点企业和优势产品，在发展规划、宣传推广、政策扶持等方面予以重点指导和支持。五、主要措施（五）加强招商引资加强招商引资进一步加大与国际国内大企业特别是央企战略投资的对接引进力度，改进招商引资模式，改“政策性招商”为“产业链招商”。五、主要措施（六）加快技术改造加快技术改造围绕高端装备领域重点，鼓励企业开展重大装备和关键配件的研制攻关和技术改造。五、主要措施（七）加快人才队伍建设加快人才队伍建设支持企业引进先进重大装备领域的领军人才和技术团队，积极落实各项人才政策。五、主要措施（八）开展交流推广活动开展交流推广活动积极推动装备制造业企业与国内外同行间的技术交流与合作，组织开展技术研讨、会展等活动，满足企业合作交流、产品销售的需要，促进我市高端装备、优势产品特别是首台（套）装备的推广应用，提升装备制造业的影响力。六、政策保障（五）营造政策服务环境营造政策服务环境加强组织协调，及时研究解决高端装备制造业发展过程中出现的问题，研究制订推动产业健康有序发展的相关政策和措施。\n"]}], "source": ["# -----------Extracting main texts for market-based instrument_Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "import nltk\n", "import jieba\n", "\n", "path1 = './policy texts (10 policies for coding)/Policy_instrument/MB/'\n", "path_list=os.listdir(path1)\n", "path_list.sort() \n", "\n", "#Define keyeords for policy instruments\n", "keywords = ['停业整顿', '关停', '关闭', '问责', '追究', '责任', '处分', '整改', '罚款', '处罚', '注销', '吊销', '准入', \\\n", "               '禁止', '新上', '进入', '落实', '强化', '推动', '巩固', '置换', '替代', '淘汰', '兼并', '重组', '压减', '强制公开', \\\n", "                '负面清单', '核准', '支持', '价格措施', '制度', '细则', '扩大', '体系', '试点', '核查', '管理', '分配', '机制', \\\n", "                '履约', '去产能', '严查', '倒逼', '上大压小', '任务分解', '标准', '资金', '技术改造', '投资', '预算', '财政', '奖励', '贷款', \\\n", "                '贴息', '金融', '融资', '债券', '基金', '担保', '保险', '资本', '股权', '信贷', '价格措施', '差别电价', '阶梯电价', '财税', \\\n", "                '投入', '税收', '补贴', '免税', '风险补偿', '债务融资', '减免', '优惠', '碳排放权交易', '碳市场', '配额', '碳排放报告', '碳排放核查']\n", "\n", "\n", "#Define function:\"get_keywords\" to locate and Get key sentences\n", "def get_keywords(instrument):\n", "    with open(path1 +  instrument, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        Instrument = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in keywords:\n", "                if i in x:\n", "                    Instrument.append(x)\n", "    #Drop duplicates\n", "    lists_instrument  = sorted(set(Instrument),key=Instrument.index)\n", "    #Drop tedious puctuations\n", "    list_instrument1 = '。'.join(lists_instrument)+'。'\n", "    instrument_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', list_instrument1)\n", "    return instrument, instrument_new, lists_instrument\n", "\n", "#Define function:\"sent_tokenizer\" to split sentences\n", "def sent_tokenizer(instrument_new):\n", "    content = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', instrument_new)\n", "    start=0\n", "    i=0\n", "    sentences=[]\n", "    punt_list='!?。！？'\n", "\n", "    for text in content:\n", "        token=content[start:i+2]\n", "        if text in punt_list and token not in punt_list: \n", "            sentences.append(content[start:i+1])\n", "            start=i+1\n", "            i+=1\n", "        else:\n", "            i+=1\n", "            token=list(token).pop()\n", "    if start<len(content):\n", "        sentences.append(content[start:])\n", "    return sentences\n", "\n", "#Define function\"stopwordslist\" to load storwords\n", "def stopwordslist(filepath):\n", "    stopwords = [line.strip() for line in open(filepath, 'r', encoding='utf-8').readlines()]\n", "    return stopwords\n", "\n", "#Get high-frequency phrases\n", "save_instrument=[]\n", "save_instrument1=[]\n", "for Instrument in path_list:\n", "    Guo_instrument, instrument_new, lists_instrument= get_keywords(Instrument)\n", "    num1 = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '措施', '审批', '核准', '备案', '监察']\n", "    c_new1 = []\n", "    for x in lists_instrument:\n", "        for i in num1: \n", "            if i in x:\n", "                c_new1.append(x)\n", "    #Drop duplicates\n", "    c_new2  = sorted(set(c_new1),key=c_new1.index)\n", "    c_new22 = '。'.join(c_new2)+'。'\n", "    c_new3 = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', c_new22)\n", "\n", "    stopwords = stopwordslist('Part 3.3 Stopwords.txt')\n", "    #Split sentences\n", "    sentence=sent_tokenizer(c_new3)\n", "    words=[w for sentence in sentence for w in jieba.cut(sentence) if w not in stopwords if len(w)>1 and w!='\\t']#词语，非单词词，同时非符号\n", "    #Calculate frequency\n", "    wordfre=nltk.FreqDist(words)\n", "    #Get top 50 phrases with highest frequency\n", "    topn_words=[w[0] for w in sorted(wordfre.items(),key=lambda d:d[1],reverse=True)][:50]\n", "\n", "    #Define the function\"_score_sentences\" to Score sentences\n", "    def _score_sentences(sentences,topn_words):#sentences：sentences after splicting; topn_words：top 50 phrases with high-frequency\n", "        scores=[]\n", "        sentence_idx=-1 #mark the 1st senctnce\n", "        for s in [list(jieba.cut(s)) for s in sentences]:# traverse all sentences\n", "            sentence_idx+=1 \n", "            word_idx=[]#save the position of high-frequency phrases in sentences\n", "            for w in topn_words:\n", "                try:\n", "                    word_idx.append(s.index(w))\n", "                except ValueError:#sentences without high-frequency phrases\n", "                    pass\n", "            word_idx.sort()\n", "            if len(word_idx)==0:\n", "                continue\n", "\n", "            #Define cluster based on the distance between positions between high-frequency phrases in the sentence\n", "            clusters=[] \n", "            cluster=[word_idx[0]] \n", "            i=1\n", "            while i<len(word_idx):\n", "                CLUSTER_THRESHOLD=2\n", "                if word_idx[i]-word_idx[i-1]<CLUSTER_THRESHOLD: #belongs to same cluster if the distance is less than the threshold\n", "                    cluster.append(word_idx[i])\n", "                else:\n", "                    clusters.append(cluster[:])#other cluster\n", "                    cluster=[word_idx[i]] \n", "                i+=1\n", "            clusters.append(cluster)\n", "            \n", "            #Score each cluster\n", "            max_cluster_score=0\n", "            for c in clusters:\n", "                significant_words_in_cluster=len(c)\n", "                total_words_in_cluster=c[-1]-c[0]+1\n", "                score=1.0*significant_words_in_cluster*significant_words_in_cluster/total_words_in_cluster\n", "                if score>max_cluster_score:\n", "                    max_cluster_score=score\n", "            scores.append((sentence_idx,max_cluster_score))\n", "        return scores;\n", "    scored_sentences=_score_sentences(sentence,topn_words)\n", "\n", "    #Sort sentences with score in top 20 \n", "    top_n_scored=sorted(scored_sentences,key=lambda s:s[1])[-10:]\n", "    top_n_scored=sorted(top_n_scored,key=lambda s:s[0])\n", "    c= dict(top_n_summary=[sentence[idx] for (idx,score) in top_n_scored])\n", "    c_new111 = ''.join(c['top_n_summary'])+''\n", "    c_new333 = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', c_new111)\n", "    print(c_new333)\n", "\n", "    #Keep 512 words in the medium of the text, if texts are longer than 512\n", "    cc = int(len(c_new333))/2\n", "    cnew_split = c_new333[int(cc-256):int(cc+256)]\n", "    if len(c_new333)<=512:\n", "        instrument_key = c_new333\n", "    else:\n", "        instrument_key = cnew_split\n", "    dic_a = {'filename': Guo_instrument, 'instrument': instrument_key}\n", "    dic_a1 = {instrument_key}\n", "    save_instrument.append(dic_a)\n", "    save_instrument1.append(dic_a1)\n", "\n", "df_a = pd.DataFrame(save_instrument)\n", "df_a1 = pd.DataFrame(save_instrument1)\n", "A = pd.concat([df_a], axis = 1)\n", "<PERSON>.to_excel(\"./output/City_MB_instrument.xlsx\", index=False)\n", "df_a1.to_csv('./output/City_MB_instrument.txt',sep=' ',index=0,header=0)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["二、市经贸委市经贸委1．积极发展循环经济，督促、指导各辖市、区重点抓好16家循环经济试点工作和100家企业清洁生产审核。2．根据国家产业政策目录，全市重点用能企业淘汰200台（套）落后用能设备，并对各辖市、区进展情况进行督查。6．根据《关于贯彻江苏省预拌砂浆生产和使用管理办法的实施意见》和《关于印发常州市建筑工程生产使用预拌砂浆若干规定》，推进东至青洋路、南至新312国道、西至外环路、北至河海（东、中、西）路，以及钟楼经济开发区、武进中心区和大学城等地区全部使用预拌砂浆。八、市交通局（地方海事局）市交通局（地方海事局）1．加大对太湖船舶污染防治的监管力度，严把入湖船舶签证关，加强对进入太湖水域船舶防污设施装备及使用情况的检查。2．加强对太湖船舶油废水和垃圾收集管理，依法查处船舶向太湖水域倾倒污染物的违法行为。5．提高农业标准化生产水平，全面推广测土配方施肥、农药减量增效控污等先进适用技术，全市测土配方施肥覆盖率达68%，重大病虫害专业化防治面积达68%，推广绿肥种植2.9万亩，施用有机肥1.16万吨，农田杀虫灯建设12500亩，全市全年化学农药、化学氮肥使用量分别比2007年削减14%、9%，使用量分别控制在4306吨和53206吨以内。大力推广秸秆还田、食用菌生产、固化成型、热解气化、沤沼还田、饲料青贮等技术，提高秸秆综合利用水平，督促、指导金坛市、溧阳市、武进区各建设2个秸秆综合利用示范镇，新北区建成2个秸秆综合利用示范镇试点。十八、市工商局金坛、溧阳、武进各完成2个秸秆综合利用示范镇建设，新北区建成2个秸秆综合利用示范镇试点。4.循环经济试点全市建设循环经济试点单位16家。2009年8月市环保局/2．排污口规范化整治完成国控、省控重点污染源的雨污分流和排污口规范化整治；并安装在线监控装置，同时加强对监控装置的维护管理，确保监控装置的联网率、运行率达100%，完好率达90%。\n"]}], "source": ["# -----------Extracting main texts for composite instrument_Python 3.10.8------------\n", "import re\n", "import os\n", "import pandas as pd\n", "import nltk\n", "import jieba\n", "\n", "path1 = './policy texts (10 policies for coding)/Policy_instrument/Composite/'\n", "path_list=os.listdir(path1)\n", "path_list.sort() \n", "\n", "#Define keyeords for policy instruments\n", "keywords = ['停业整顿', '关停', '关闭', '问责', '追究', '责任', '处分', '整改', '罚款', '处罚', '注销', '吊销', '准入', \\\n", "               '禁止', '新上', '进入', '落实', '强化', '推动', '巩固', '置换', '替代', '淘汰', '兼并', '重组', '压减', '强制公开', \\\n", "                '负面清单', '核准', '支持', '价格措施', '制度', '细则', '扩大', '体系', '试点', '核查', '管理', '分配', '机制', \\\n", "                '履约', '去产能', '严查', '倒逼', '上大压小', '任务分解', '标准', '资金', '技术改造', '投资', '预算', '财政', '奖励', '贷款', \\\n", "                '贴息', '金融', '融资', '债券', '基金', '担保', '保险', '资本', '股权', '信贷', '价格措施', '差别电价', '阶梯电价', '财税', \\\n", "                '投入', '税收', '补贴', '免税', '风险补偿', '债务融资', '减免', '优惠', '碳排放权交易', '碳市场', '配额', '碳排放报告', '碳排放核查']\n", "\n", "\n", "#Define function:\"get_keywords\" to locate and Get key sentences\n", "def get_keywords(instrument):\n", "    with open(path1 +  instrument, 'r', encoding=\"utf-8\", errors='ignore') as f:\n", "        content = f.read().strip('\\n')\n", "        Instrument = []\n", "        sentences = re.split(r'\\!|\\?|。|]|！|？|\\.{6}', content)\n", "        for x in sentences:\n", "            for i in keywords:\n", "                if i in x:\n", "                    Instrument.append(x)\n", "    #Drop duplicates\n", "    lists_instrument  = sorted(set(Instrument),key=Instrument.index)\n", "    #Drop tedious puctuations\n", "    list_instrument1 = '。'.join(lists_instrument)+'。'\n", "    instrument_new = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', list_instrument1)\n", "    return instrument, instrument_new, lists_instrument\n", "\n", "#Define function:\"sent_tokenizer\" to split sentences\n", "def sent_tokenizer(instrument_new):\n", "    content = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', instrument_new)\n", "    start=0\n", "    i=0\n", "    sentences=[]\n", "    punt_list='!?。！？'\n", "\n", "    for text in content:\n", "        token=content[start:i+2]\n", "        if text in punt_list and token not in punt_list: \n", "            sentences.append(content[start:i+1])\n", "            start=i+1\n", "            i+=1\n", "        else:\n", "            i+=1\n", "            token=list(token).pop()\n", "    if start<len(content):\n", "        sentences.append(content[start:])\n", "    return sentences\n", "\n", "#Define function\"stopwordslist\" to load storwords\n", "def stopwordslist(filepath):\n", "    stopwords = [line.strip() for line in open(filepath, 'r', encoding='utf-8').readlines()]\n", "    return stopwords\n", "\n", "#Get high-frequency phrases\n", "save_instrument=[]\n", "save_instrument1=[]\n", "for Instrument in path_list:\n", "    Guo_instrument, instrument_new, lists_instrument= get_keywords(Instrument)\n", "    num1 = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '措施', '审批', '核准', '备案', '监察']\n", "    c_new1 = []\n", "    for x in lists_instrument:\n", "        for i in num1: \n", "            if i in x:\n", "                c_new1.append(x)\n", "    #Drop duplicates\n", "    c_new2  = sorted(set(c_new1),key=c_new1.index)\n", "    c_new22 = '。'.join(c_new2)+'。'\n", "    c_new3 = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', c_new22)\n", "\n", "    stopwords = stopwordslist('Part 3.3 Stopwords.txt')\n", "    #Split sentences\n", "    sentence=sent_tokenizer(c_new3)\n", "    words=[w for sentence in sentence for w in jieba.cut(sentence) if w not in stopwords if len(w)>1 and w!='\\t']#词语，非单词词，同时非符号\n", "    #Calculate frequency\n", "    wordfre=nltk.FreqDist(words)\n", "    #Get top 50 phrases with highest frequency\n", "    topn_words=[w[0] for w in sorted(wordfre.items(),key=lambda d:d[1],reverse=True)][:50]\n", "\n", "    #Define the function\"_score_sentences\" to Score sentences\n", "    def _score_sentences(sentences,topn_words):#sentences：sentences after splicting; topn_words：top 50 phrases with high-frequency\n", "        scores=[]\n", "        sentence_idx=-1 #mark the 1st senctnce\n", "        for s in [list(jieba.cut(s)) for s in sentences]:# traverse all sentences\n", "            sentence_idx+=1 \n", "            word_idx=[]#save the position of high-frequency phrases in sentences\n", "            for w in topn_words:\n", "                try:\n", "                    word_idx.append(s.index(w))\n", "                except ValueError:#sentences without high-frequency phrases\n", "                    pass\n", "            word_idx.sort()\n", "            if len(word_idx)==0:\n", "                continue\n", "\n", "            #Define cluster based on the distance between positions between high-frequency phrases in the sentence\n", "            clusters=[] \n", "            cluster=[word_idx[0]] \n", "            i=1\n", "            while i<len(word_idx):\n", "                CLUSTER_THRESHOLD=2\n", "                if word_idx[i]-word_idx[i-1]<CLUSTER_THRESHOLD: #belongs to same cluster if the distance is less than the threshold\n", "                    cluster.append(word_idx[i])\n", "                else:\n", "                    clusters.append(cluster[:])#other cluster\n", "                    cluster=[word_idx[i]] \n", "                i+=1\n", "            clusters.append(cluster)\n", "            \n", "            #Score each cluster\n", "            max_cluster_score=0\n", "            for c in clusters:\n", "                significant_words_in_cluster=len(c)\n", "                total_words_in_cluster=c[-1]-c[0]+1\n", "                score=1.0*significant_words_in_cluster*significant_words_in_cluster/total_words_in_cluster\n", "                if score>max_cluster_score:\n", "                    max_cluster_score=score\n", "            scores.append((sentence_idx,max_cluster_score))\n", "        return scores;\n", "    scored_sentences=_score_sentences(sentence,topn_words)\n", "\n", "    #Sort sentences with score in top 20 \n", "    top_n_scored=sorted(scored_sentences,key=lambda s:s[1])[-10:]\n", "    top_n_scored=sorted(top_n_scored,key=lambda s:s[0])\n", "    c= dict(top_n_summary=[sentence[idx] for (idx,score) in top_n_scored])\n", "    c_new111 = ''.join(c['top_n_summary'])+''\n", "    c_new333 = re.sub(r'\\[|\\--|\\【|\\】|\\s|\\t|\\n', '', c_new111)\n", "    print(c_new333)\n", "\n", "    #Keep 512 words in the medium of the text, if texts are longer than 512\n", "    cc = int(len(c_new333))/2\n", "    cnew_split = c_new333[int(cc-256):int(cc+256)]\n", "    if len(c_new333)<=512:\n", "        instrument_key = c_new333\n", "    else:\n", "        instrument_key = cnew_split\n", "    dic_a = {'filename': Guo_instrument, 'instrument': instrument_key}\n", "    dic_a1 = {instrument_key}\n", "    save_instrument.append(dic_a)\n", "    save_instrument1.append(dic_a1)\n", "\n", "df_a = pd.DataFrame(save_instrument)\n", "df_a1 = pd.DataFrame(save_instrument1)\n", "A = pd.concat([df_a], axis = 1)\n", "<PERSON>.to_excel(\"./output/City_Composite_instrument.xlsx\", index=False)\n", "df_a1.to_csv('./output/City_Composite_instrument.txt',sep=' ',index=0,header=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Part 4: Manual labelling"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* This paper conducts manual labelling on the basis of a termination named \"doccano\".\n", "\n", "* Installation and guideline for usage, please refer to \n", "https://github.com/PaddlePaddle/PaddleNLP/blob/develop/applications/text_classification/doccano.md\n", "\n", "* We upload the \"Textline\" file format and downlowd the labelling result in \".jsonl\" file for model training.\n", "\n", "* Labels for different objectives and instruments are set as:\n", "    1) Carbon reduction: Carbon_3 Carbon_2 Carbon_1\n", "    2) Energy conservation: Energy_4 Energy_3 Energy_2 Energy_1\n", "    3) Capacity utilization: CU_3 CU_2 CU_1\n", "    4) Technology: Tech_3 Tech_2 Tech_1\n", "    5) Command-and-control: CC_3 CC_2 CC_1\n", "    6) Market-based: MB_3 MB_2 MB_1\n", "    7) Composite instruments are respectively scored on the basis of two instruments, and take the maximum value as the intensity of composite instrument."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Part 5: Prompt learning and prediction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* This paper applies \"ernie-3.0-base-zh\" model of prompt learning to train the model and predict the intensity for unlabelled policies.\n", "* The code in this part is executed with GPU.\n", "https://github.com/PaddlePaddle/PaddleNLP/tree/develop/applications/text_classification/multi_class/few-shot\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 5.0 Data preparation"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["e:\\Python\\lib\\site-packages\\_distutils_hack\\__init__.py:33: UserWarning: Setuptools is replacing distutils.\n", "  warnings.warn(\"Setuptools is replacing distutils.\")\n", "\n", "  0%|          | 0/699 [00:00<?, ?it/s]\n", "  0%|          | 0/699 [00:00<?, ?it/s]\n", "\u001b[32m[2023-07-27 00:01:14,807] [    INFO]\u001b[0m - Save 559 examples to ./Samples_for_prompt_learning/data/train.txt.\u001b[0m\n", "\u001b[32m[2023-07-27 00:01:14,808] [    INFO]\u001b[0m - Save 70 examples to ./Samples_for_prompt_learning/data/dev.txt.\u001b[0m\n", "\u001b[32m[2023-07-27 00:01:14,809] [    INFO]\u001b[0m - Save 70 examples to ./Samples_for_prompt_learning/data/test.txt.\u001b[0m\n", "\u001b[32m[2023-07-27 00:01:14,810] [    INFO]\u001b[0m - Save 70 examples to ./Samples_for_prompt_learning/data/data.txt.\u001b[0m\n", "\u001b[32m[2023-07-27 00:01:14,810] [    INFO]\u001b[0m - Finished! It takes 0.02 seconds\u001b[0m\n"]}], "source": ["# -----------Data preparation_Python 3.10.8------------\n", "#Split data (80% are splited into training set),and copy files into folder sparse_aug and data_aug \n", "!python \"./Samples_for_prompt_learning/doccano.py\" \\\n", "--doccano_file \"./Samples_for_prompt_learning/data/Sample-MB.jsonl\" \\\n", "--save_dir \"./Samples_for_prompt_learning/data/\" \\\n", "--splits 0.8 0.1 0.1 \\\n", "--task_type \"multi_class\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------0. Update package----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!pip install --upgrade paddlenlp \n", "!pip install scikit-learn\n", "!pip install paddlenlp==2.5.2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 5.1 Prompt learning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Prompt learning----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!python \"./Samples_for_prompt_learning/train.py\" --dataset_dir \"./Samples_for_prompt_learning/data/\"  \\\n", "#--output_dir ./checkpoints/ \\\n", "--prompt \"this label is\" \\\n", "--max_seq_length 512  \\\n", "--learning_rate 3e-5 \\\n", "--ppt_learning_rate 3e-4 \\\n", "--do_train \\\n", "--do_eval \\\n", "--logging_steps 5 \\\n", "--per_device_eval_batch_size 32 \\\n", "--per_device_train_batch_size 8 \\\n", "--do_predict \\\n", "\\\n", "--model_name_or_path ernie-3.0-base-zh \\\n", "--num_train_epochs 100 \\\n", "--metric_for_best_model accuracy \\\n", "--load_best_model_at_end \\\n", "--evaluation_strategy epoch \\\n", "--save_strategy epoch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 5.2 Model evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Model evaluation----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!python \"./Samples_for_prompt_learning/analysis/evaluate.py\" \\\n", "--device \"gpu\" \\\n", "--max_seq_length 512 \\\n", "--batch_size 32 \\\n", "--bad_case_path \"./Samples_for_prompt_learning/bad_case.txt\" \\\n", "--dataset_dir \"./Samples_for_prompt_learning/data/\" \\\n", "--params_path \"./checkpoint/\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 5.3 Model optimization for solving data sparcity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Update package----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!pip install trustai==0.1.4 --user\n", "!pip list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Augmentation for data sparsity----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!python \"./Samples_for_prompt_learning/analysis/sparse.py\" \\\n", "--device \"gpu\" \\\n", "--dataset_dir \"./Samples_for_prompt_learning/data/\" \\\n", "--aug_strategy \"substitute\" \\\n", "--max_seq_length 512 \\\n", "--params_path \"./checkpoint/\" \\\n", "--batch_size 16 \\\n", "--sparse_num 100 \\\n", "--support_num 100\\\n", "--sparse_file \"sparse.txt\"\\\n", "--support_file \"support.txt\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------merge new training set for prompt learning----------------------------\n", "!cat ./Samples_for_prompt_learning/data/train.txt ./Samples_for_prompt_learning/data/support.txt > ./Samples_for_prompt_learning/sparse_aug/train.txt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Prompt learning: new training----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!python \"./Samples_for_prompt_learning/train.py\" --dataset_dir \"./Samples_for_prompt_learning/sparse_aug/\"  \\\n", "#--output_dir ./checkpoints/ \\\n", "--prompt \"this label is\" \\\n", "--max_seq_length 512  \\\n", "--learning_rate 3e-5 \\\n", "--ppt_learning_rate 3e-4 \\\n", "--do_train \\\n", "--do_eval \\\n", "--logging_steps 5 \\\n", "--per_device_eval_batch_size 32 \\\n", "--per_device_train_batch_size 8 \\\n", "--do_predict \\\n", "\\\n", "--model_name_or_path ernie-3.0-base-zh \\\n", "--num_train_epochs 100 \\\n", "--metric_for_best_model accuracy \\\n", "--load_best_model_at_end \\\n", "--evaluation_strategy epoch \\\n", "--save_strategy epoch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 5.4 Model optimization for solving class-imbalance\n", "* https://github.com/PaddlePaddle/PaddleNLP/blob/develop/docs/dataaug.md"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------solving class-imbalance through data augmentation----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "!python \"./Samples_for_prompt_learning/analysis/aug.py\" \\\n", "--create_n 2 \\\n", "--aug_percent 0.1 \\\n", "--train_path \"./Samples_for_prompt_learning/data/train.txt\" \\\n", "--aug_path \"./Samples_for_prompt_learning/data/aug.txt\"\\\n", "--aug_type \"mlm\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------merge new training set2 for prompt learning----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "!cat ./Samples_for_prompt_learning/data/aug.txt ./Samples_for_prompt_learning/sparse_aug/train.txt > ./Samples_for_prompt_learning/data_aug/train.txt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Prompt learning: new training----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "\n", "!python \"./Samples_for_prompt_learning/train.py\" --dataset_dir \"./Samples_for_prompt_learning/data_aug/\"  \\\n", "#--output_dir ./checkpoints/ \\\n", "--prompt \"this label is\" \\\n", "--max_seq_length 512  \\\n", "--learning_rate 3e-5 \\\n", "--ppt_learning_rate 3e-4 \\\n", "--do_train \\\n", "--do_eval \\\n", "--logging_steps 5 \\\n", "--per_device_eval_batch_size 32 \\\n", "--per_device_train_batch_size 8 \\\n", "--do_predict \\\n", "\\\n", "--model_name_or_path ernie-3.0-base-zh \\\n", "--num_train_epochs 100 \\\n", "--metric_for_best_model accuracy \\\n", "--load_best_model_at_end \\\n", "--evaluation_strategy epoch \\\n", "--save_strategy epoch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part 5.5 Prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ----------------------------Prediction----------------------------\n", "#GPU: Tesla V100, 16GB; Python 3.10.10 on Paddlepaddle\n", "#Compare the accuracy for three models above, and choose the model with highest accuracy for prediction\n", "!python predict.py \\\n", "--device \"gpu\" \\\n", "--max_seq_length 512 \\\n", "--batch_size 32 \\\n", "#Choose the model with highest accuracy for prediction\n", "--dataset_dir \"./Samples_for_prompt_learning/data_aug/predict\" \\\n", "#--dataset_dir \"./Samples_for_prompt_learning/sparse_aug/predict\" \\\n", "#--dataset_dir \"./Samples_for_prompt_learning/data/predict\" \\\n", "--params_path  \"./checkpoint/\" \\\n", "--output_file \"output.txt\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}