cd "C:\Users\<USER>\Desktop\Low_carbon_policy_intensity_code\policy texts (10 policies for coding)"

*********************************************1. For policies with headings and subheadings*********************************************

foreach u of numlist 1 3 4 5 7 9 10{
	use city_`u'.dta
	gen mark1=0
	*mark1: Headings with keywords for policy objective：
	replace mark1=1 if strmatch(titlenew21 ,"*目标*")==1 | strmatch(titlenew21 ,"*任务*")==1 | strmatch(titlenew21 ,"*指标*")==1 | strmatch(titlenew21 ,"*产业政策导向*")==1
	gen mark11=0
	*mark11: Subheadings with keywords for policy objective
	replace mark11=1 if strmatch(titlenew11 ,"*目标*")==1 | strmatch(titlenew11 ,"*任务*")==1 | strmatch(titlenew11 ,"*指标*")==1| strmatch(titlenew11 ,"*产业政策导向*")==1
	gen mark111=0
	*mark111: Contents with keywords for policy objective
	replace mark111=1 if strmatch(content ,"*目标*")==1 | strmatch(content ,"*任务*")==1 | strmatch(content ,"*指标*")==1| strmatch(content,"*产业政策导向*")==1
	egen mark1_p=count(mark1) if mark1==0
	replace mark1_p = mark1_p[_n-1] if mark1_p ==.
	gen N=_N
	*Generate variable "gap" for headings: gap==0 means headings do not have keywords for policy objectives.
	gen gap=N-mark1_p
	replace gap=1 if gap!=0

	*Generate variable "gap1" for subheadings: gap1==0 means subheadings do not have keywords for policy objectives.
	egen mark11_p=count(mark11) if mark11==0
	replace mark11_p = mark11_p[_n-1] if mark11_p ==.
	gen gap1=N-mark11_p
	replace gap1=1 if gap1!=0

	
    *Generate variable "save" to summarize possible situations.
	gen save=.
	*Use mark111 as the criteria to generate policy objective file if neither headings nor subheadings have keywords for policy objectives.
	replace save=mark111 if gap==0 & gap1==0
	*Use mark1 to generate policy objective file if headings have keywords for objectives but subheadings do not have.
	replace save=mark1 if gap !=0 & gap1==0
	*Use mark1 to generate policy objective file if headings do not have keywords for objectives but subheadings have.
	replace save=mark11 if gap==0 & gap1!=0
	*Use the union set of mark1 and mark11 to generate policy objective file if both headings and subheadings have keywords for objectives.
	gen mark12=mark1+mark11
	replace save=mark12 if gap !=0 & gap1!=0
	replace save=1 if save>1
	
***Get policy objectives***
	preserve
	keep if save==1
	*sort texts of policy objective by subheading, heading, contents
	gen order_0 = _n
	gen order=.
	replace order=1 if mark11==1
	replace order=2 if mark1==1 & mark11 !=1
	replace order=3 if mark111==1 & mark1 !=1 & mark11 !=1
	sort order order_0
	
	set obs 2500
	keep title titlenew21 titlenew11 content
	gen new= titlenew21+titlenew11+content

	gen check="。"
	keep new check
	duplicates drop new check,force
	export delimited using G_city_`u'.txt, novarnames replace
	restore
	
	
***Get policy instruments***
	*drop texts for policy objective
	drop if save==1
	gen mark2=1
	*drop expressions related to backgrounds
	replace mark2=0 if strmatch(titlenew21 ,"*现状*")==1 | strmatch(titlenew21 ,"*意义*")==1| strmatch(titlenew21 ,"*形势*")==1 | strmatch(titlenew21 ,"*指导思想*")==1 | strmatch(titlenew21 ,"*基本原则*")==1 | strmatch(content ,"*【法宝引证码】*")==1 | strmatch(titlenew21 ,"*总体思路*")==1 | strmatch(titlenew21 ,"*总体要求*")==1 | strmatch(titlenew21 ,"*背景*")==1
	replace mark2=0 if strmatch(titlenew11 ,"*现状*")==1 | strmatch(titlenew11 ,"*意义*")==1| strmatch(titlenew11 ,"*形势*")==1 | strmatch(titlenew11 ,"*指导思想*")==1 | strmatch(titlenew11 ,"*基本原则*")==1 | strmatch(content ,"*【法宝引证码】*")==1 | strmatch(titlenew11 ,"*总体思路*")==1 | strmatch(titlenew11 ,"*总体要求*")==1 | strmatch(titlenew11 ,"*背景*")==1

	keep if mark2==1
	keep title titlenew21 titlenew11 content
	gen new= titlenew21+titlenew11+content
    set obs 2500
	gen check="。"
	keep new check
	duplicates drop new check,force
	export delimited using A_city_`u'.txt, novarnames replace
	clear
}


*********************************************2. For policies without headings and subheadings*********************************************

foreach u of numlist 2 6 8 {
	use city_`u'.dta
	*mark111: Contents with keywords for policy objective
	gen mark111=0
	replace mark111=1 if strmatch(content ,"*目标*")==1 | strmatch(content ,"*任务*")==1 | strmatch(content ,"*指标*")==1| strmatch(content,"*产业政策导向*")==1

***Get policy objectives***
	preserve
	keep if mark111==1
	set obs 2500
	gen check="。"
	keep content check
	duplicates drop content check,force
	export delimited using G_city_`u'.txt, novarnames replace
	restore
	
***Get policy instruments***
    *drop texts for policy objective
	drop if mark111==1
	gen mark2=1
	*drop expressions related to backgrounds
	replace mark2=0 if strmatch(title ,"*现状*")==1 | strmatch(title,"*意义*")==1| strmatch(title,"*形势*")==1 | strmatch(title,"*指导思想*")==1 | strmatch(title,"*基本原则*")==1 | strmatch(content ,"*【法宝引证码】*")==1 | strmatch(title ,"*总体思路*")==1 | strmatch(title ,"*总体要求*")==1 | strmatch(title,"*背景*")==1
	gen new= title+content
	set obs 2500
	gen check="。"
	keep new check
	duplicates drop new check,force
	export delimited using A_city_`u'.txt, novarnames replace
	clear
}