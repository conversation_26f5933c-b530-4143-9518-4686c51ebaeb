#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的政策结构化信息提取系统
基于原项目技术，实现您需求的四个维度信息提取
"""

import re
import json
import pandas as pd
from typing import Dict, List, Tuple, Any
import jieba
from collections import defaultdict

class CompletePolicyExtractor:
    """完整的政策信息提取器"""
    
    def __init__(self):
        """初始化提取器"""
        self.setup_extraction_patterns()
        self.load_keywords_dictionaries()
    
    def setup_extraction_patterns(self):
        """设置提取模式"""
        # 基于原项目的目标识别模式
        self.target_patterns = {
            'direct_targets': [
                r'目标[是为：:]\s*(.{1,100}?)[。；\n]',
                r'到(\d{4})年[，,]?(.{1,100}?)[。；\n]',
                r'力争(.{1,100}?)[。；\n]',
                r'确保(.{1,100}?)[。；\n]',
                r'实现(.{1,100}?)[。；\n]'
            ],
            'quantitative_targets': [
                r'(\d+(?:\.\d+)?%)',
                r'(\d+(?:\.\d+)?万吨)',
                r'(\d+(?:\.\d+)?亿元)',
                r'(\d+(?:\.\d+)?万元)',
                r'下降(\d+(?:\.\d+)?%)',
                r'增长(\d+(?:\.\d+)?%)',
                r'减少(\d+(?:\.\d+)?%)'
            ]
        }
        
        # 人群/行业识别模式
        self.target_group_patterns = {
            'individual': [
                r'个人|居民|公民|消费者|用户|市民|群众|家庭',
                r'全体.*?人员|所有.*?人员'
            ],
            'enterprise': [
                r'企业|公司|厂家|生产商|经营者|制造商|供应商',
                r'工业企业|服务企业|民营企业|国有企业|外资企业',
                r'重点企业|规模以上企业|中小企业'
            ],
            'industry': [
                r'钢铁|水泥|化工|石化|电力|煤炭|有色金属',
                r'建筑|交通|物流|金融|房地产|旅游',
                r'制造业|服务业|农业|林业|渔业'
            ],
            'organization': [
                r'行业协会|商会|联盟|组织|机构|团体',
                r'科研院所|高等院校|事业单位'
            ],
            'government': [
                r'政府|部门|机关|单位|管理部门|职能部门',
                r'各级政府|地方政府|市政府|区县政府',
                r'相关部门|有关部门|主管部门'
            ]
        }
        
        # 部门识别模式
        self.department_patterns = {
            'implementation': [
                r'由(.{1,20}?)负责',
                r'(.{1,20}?)牵头',
                r'(.{1,20}?)组织实施',
                r'(.{1,20}?)统筹',
                r'(.{1,20}?)主管',
                r'委托(.{1,20}?)执行'
            ],
            'supervision': [
                r'(.{1,20}?)监督',
                r'(.{1,20}?)检查',
                r'(.{1,20}?)考核',
                r'(.{1,20}?)评估',
                r'第三方(.{1,20}?)监督',
                r'专门(.{1,20}?)监管',
                r'(.{1,20}?)审计'
            ]
        }
        
        # 措施识别模式
        self.measure_patterns = {
            'financial': [
                r'设立.*?资金.*?(\d+.*?[万亿]元)',
                r'财政.*?投入.*?(\d+.*?[万亿]元)',
                r'专项资金.*?(\d+.*?[万亿]元)',
                r'补贴.*?(\d+.*?元)',
                r'奖励.*?(\d+.*?元)',
                r'贷款|融资|投资|基金'
            ],
            'regulatory': [
                r'制定.*?标准',
                r'建立.*?制度',
                r'完善.*?机制',
                r'实施.*?管理',
                r'加强.*?监管',
                r'严格.*?执法'
            ],
            'technical': [
                r'推广.*?技术',
                r'应用.*?技术',
                r'研发.*?技术',
                r'技术改造',
                r'技术创新',
                r'科技攻关'
            ],
            'market': [
                r'市场机制',
                r'价格机制',
                r'交易.*?机制',
                r'碳排放权交易',
                r'排污权交易',
                r'绿色金融'
            ]
        }
    
    def load_keywords_dictionaries(self):
        """加载关键词词典（基于原项目）"""
        # 尝试加载原项目词典
        self.dictionaries = {}
        
        dict_files = {
            'objective': 'Part 2.1-(1) Objective_content_lexicon.txt',
            'instrument': 'Part 2.2-(1) Instrument_lexicon.txt',
            'stopwords': 'Part 3.3 Stopwords.txt'
        }
        
        for dict_name, file_path in dict_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.dictionaries[dict_name] = [line.strip() for line in f if line.strip()]
                print(f"已加载 {dict_name} 词典，包含 {len(self.dictionaries[dict_name])} 个词汇")
            except FileNotFoundError:
                print(f"未找到词典文件: {file_path}，使用默认词典")
                self.dictionaries[dict_name] = []
    
    def extract_policy_targets(self, text: str) -> Dict[str, Any]:
        """提取政策目标信息"""
        targets = {
            'direct_targets': [],
            'quantitative_targets': [],
            'target_keywords': [],
            'confidence_score': 0.0
        }
        
        # 1. 提取直接目标表述
        for pattern in self.target_patterns['direct_targets']:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    targets['direct_targets'].extend([m.strip() for m in match if m.strip()])
                else:
                    targets['direct_targets'].append(match.strip())
        
        # 2. 提取量化目标
        for pattern in self.target_patterns['quantitative_targets']:
            matches = re.findall(pattern, text)
            targets['quantitative_targets'].extend(matches)
        
        # 3. 基于原项目词典的目标识别
        if 'objective' in self.dictionaries and self.dictionaries['objective']:
            words = jieba.lcut(text)
            objective_words = set(self.dictionaries['objective'])
            found_keywords = [word for word in words if word in objective_words]
            targets['target_keywords'] = found_keywords
        
        # 4. 计算置信度
        confidence = 0.0
        if targets['direct_targets']:
            confidence += 0.4
        if targets['quantitative_targets']:
            confidence += 0.4
        if targets['target_keywords']:
            confidence += 0.2
        
        targets['confidence_score'] = min(confidence, 1.0)
        
        return targets
    
    def extract_target_groups(self, text: str) -> Dict[str, Any]:
        """提取政策针对的人群/行业"""
        groups = {
            'individual': [],
            'enterprise': [],
            'industry': [],
            'organization': [],
            'government': [],
            'coverage_score': 0.0
        }
        
        for group_type, patterns in self.target_group_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    groups[group_type].extend(matches)
        
        # 计算覆盖度
        covered_types = sum(1 for group_type in ['individual', 'enterprise', 'industry', 'organization', 'government'] 
                           if groups[group_type])
        groups['coverage_score'] = covered_types / 5.0
        
        return groups
    
    def extract_departments(self, text: str) -> Dict[str, Any]:
        """提取实施和监督部门"""
        departments = {
            'implementation': [],
            'supervision': [],
            'department_clarity': 0.0
        }
        
        # 提取实施部门
        for pattern in self.department_patterns['implementation']:
            matches = re.findall(pattern, text)
            departments['implementation'].extend([match.strip() for match in matches])
        
        # 提取监督部门
        for pattern in self.department_patterns['supervision']:
            matches = re.findall(pattern, text)
            departments['supervision'].extend([match.strip() for match in matches])
        
        # 计算部门明确度
        clarity = 0.0
        if departments['implementation']:
            clarity += 0.5
        if departments['supervision']:
            clarity += 0.5
        
        departments['department_clarity'] = clarity
        
        return departments
    
    def extract_specific_measures(self, text: str) -> Dict[str, Any]:
        """提取具体措施"""
        measures = {
            'financial': [],
            'regulatory': [],
            'technical': [],
            'market': [],
            'measure_diversity': 0.0
        }
        
        for measure_type, patterns in self.measure_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    measures[measure_type].extend(matches)
        
        # 计算措施多样性
        diversity = sum(1 for measure_type in ['financial', 'regulatory', 'technical', 'market'] 
                       if measures[measure_type])
        measures['measure_diversity'] = diversity / 4.0
        
        return measures
    
    def extract_complete_structure(self, text: str, policy_id: str = None) -> Dict[str, Any]:
        """完整的结构化信息提取"""
        # 文本预处理
        text = self.preprocess_text(text)
        
        # 提取各维度信息
        targets = self.extract_policy_targets(text)
        groups = self.extract_target_groups(text)
        departments = self.extract_departments(text)
        measures = self.extract_specific_measures(text)
        
        # 整合结果
        result = {
            'policy_id': policy_id,
            'policy_targets': targets,
            'target_groups': groups,
            'departments': departments,
            'specific_measures': measures,
            'extraction_summary': {
                'target_confidence': targets['confidence_score'],
                'group_coverage': groups['coverage_score'],
                'department_clarity': departments['department_clarity'],
                'measure_diversity': measures['measure_diversity']
            }
        }
        
        return result
    
    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 去除多余空白字符
        text = re.sub(r'\s+', ' ', text)
        # 去除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\s\d\.\%]', '', text)
        return text.strip()
    
    def batch_extract(self, policy_texts: Dict[str, str]) -> pd.DataFrame:
        """批量提取结构化信息"""
        results = []
        
        for policy_id, text in policy_texts.items():
            result = self.extract_complete_structure(text, policy_id)
            
            # 展平结果用于DataFrame
            flat_result = {
                'policy_id': policy_id,
                
                # 目标信息
                'direct_targets_count': len(result['policy_targets']['direct_targets']),
                'quantitative_targets_count': len(result['policy_targets']['quantitative_targets']),
                'target_confidence': result['policy_targets']['confidence_score'],
                
                # 群体信息
                'individual_mentioned': len(result['target_groups']['individual']) > 0,
                'enterprise_mentioned': len(result['target_groups']['enterprise']) > 0,
                'industry_mentioned': len(result['target_groups']['industry']) > 0,
                'organization_mentioned': len(result['target_groups']['organization']) > 0,
                'government_mentioned': len(result['target_groups']['government']) > 0,
                'group_coverage': result['target_groups']['coverage_score'],
                
                # 部门信息
                'implementation_dept_count': len(result['departments']['implementation']),
                'supervision_dept_count': len(result['departments']['supervision']),
                'department_clarity': result['departments']['department_clarity'],
                
                # 措施信息
                'financial_measures_count': len(result['specific_measures']['financial']),
                'regulatory_measures_count': len(result['specific_measures']['regulatory']),
                'technical_measures_count': len(result['specific_measures']['technical']),
                'market_measures_count': len(result['specific_measures']['market']),
                'measure_diversity': result['specific_measures']['measure_diversity']
            }
            
            results.append(flat_result)
        
        return pd.DataFrame(results)

# 使用示例
if __name__ == "__main__":
    extractor = CompletePolicyExtractor()
    
    # 示例政策文本
    sample_text = """
    关于加强工业企业碳排放管理的通知
    
    一、总体目标
    到2025年，全市工业企业碳排放强度较2020年下降18%，重点行业企业碳排放总量控制在500万吨以内。
    
    二、适用范围
    本政策适用于年产值超过2000万元的工业企业、重点用能单位、各级政府及其环保部门、行业协会和第三方机构。
    
    三、主要措施
    1. 设立专项资金5000万元支持企业低碳技术改造
    2. 建立严格的碳排放监管制度
    3. 推广先进节能减排技术
    4. 建立碳排放权交易机制
    
    四、组织实施
    市环保局负责统一组织实施，各区县政府具体执行。建立第三方监督评估机制，委托专业机构进行年度评估。
    """
    
    result = extractor.extract_complete_structure(sample_text, "SAMPLE_001")
    
    print("=== 完整结构化信息提取结果 ===")
    print(f"政策ID: {result['policy_id']}")
    print(f"\n1. 政策目标:")
    print(f"   直接目标: {result['policy_targets']['direct_targets']}")
    print(f"   量化目标: {result['policy_targets']['quantitative_targets']}")
    print(f"   置信度: {result['policy_targets']['confidence_score']:.2f}")
    
    print(f"\n2. 目标群体:")
    for group_type in ['individual', 'enterprise', 'industry', 'organization', 'government']:
        if result['target_groups'][group_type]:
            print(f"   {group_type}: {result['target_groups'][group_type]}")
    print(f"   覆盖度: {result['target_groups']['coverage_score']:.2f}")
    
    print(f"\n3. 实施和监督部门:")
    print(f"   实施部门: {result['departments']['implementation']}")
    print(f"   监督部门: {result['departments']['supervision']}")
    print(f"   明确度: {result['departments']['department_clarity']:.2f}")
    
    print(f"\n4. 具体措施:")
    for measure_type in ['financial', 'regulatory', 'technical', 'market']:
        if result['specific_measures'][measure_type]:
            print(f"   {measure_type}: {result['specific_measures'][measure_type]}")
    print(f"   多样性: {result['specific_measures']['measure_diversity']:.2f}")
