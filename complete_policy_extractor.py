#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的政策结构化信息提取系统
基于原项目技术，实现您需求的四个维度信息提取
"""

import re
import json
import pandas as pd
from typing import Dict, List, Tuple, Any
from collections import defaultdict

# 尝试导入jieba，如果没有则使用简单分词
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    print("jieba未安装，将使用简单分词方法")

class CompletePolicyExtractor:
    """完整的政策信息提取器"""
    
    def __init__(self):
        """初始化提取器"""
        self.setup_extraction_patterns()
        self.load_keywords_dictionaries()
    
    def setup_extraction_patterns(self):
        """设置提取模式"""
        # 低碳发展目标识别模式
        self.target_patterns = {
            'low_carbon_indirect': [
                r'发展.*?低碳', r'推进.*?绿色', r'促进.*?节能', r'加强.*?环保',
                r'提升.*?能效', r'改善.*?环境', r'优化.*?结构', r'转型.*?升级'
            ],
            'low_carbon_direct': [
                r'低碳.*?目标', r'碳.*?目标', r'节能.*?目标', r'减排.*?目标',
                r'绿色.*?目标', r'环保.*?目标', r'能耗.*?目标', r'排放.*?目标'
            ],
            'quantitative_targets': [
                r'(\d+(?:\.\d+)?%)', r'(\d+(?:\.\d+)?万吨)', r'(\d+(?:\.\d+)?亿元)',
                r'下降(\d+(?:\.\d+)?%)', r'减少(\d+(?:\.\d+)?%)', r'降低(\d+(?:\.\d+)?%)',
                r'到(\d{4})年.*?(\d+(?:\.\d+)?%)', r'(\d+(?:\.\d+)?万吨标准煤)',
                r'单位GDP.*?(\d+(?:\.\d+)?%)', r'能耗强度.*?(\d+(?:\.\d+)?%)'
            ]
        }
        
        # 目标群体识别模式（按您的要求：个人、企业、行业组织、各级政府）
        self.target_group_patterns = {
            'individual': [
                r'个人|居民|公民|消费者|用户|市民|群众|家庭|住户',
                r'全体.*?人员|所有.*?人员|广大.*?群众'
            ],
            'enterprise': [
                r'企业|公司|厂家|生产商|经营者|制造商|供应商|单位',
                r'工业企业|服务企业|民营企业|国有企业|外资企业|中小企业',
                r'重点企业|规模以上企业|用能单位|排污单位'
            ],
            'organization': [
                r'行业协会|商会|联盟|组织|机构|团体|社会组织',
                r'科研院所|高等院校|事业单位|中介机构|第三方机构'
            ],
            'government': [
                r'政府|部门|机关|管理部门|职能部门|行政部门',
                r'各级政府|地方政府|市政府|区县政府|街道|社区',
                r'相关部门|有关部门|主管部门|监管部门'
            ]
        }
        
        # 问题类型识别模式（按您的要求：5类污染防治问题）
        self.problem_type_patterns = {
            'coal_pollution': [
                r'燃煤|煤炭|锅炉|供热|燃烧|煤电|火电|散煤',
                r'煤炭.*?污染|燃煤.*?治理|锅炉.*?改造'
            ],
            'industrial_pollution': [
                r'工业.*?污染|生产.*?污染|制造.*?污染|化工.*?污染',
                r'钢铁|水泥|石化|有色金属|造纸|印染|电镀'
            ],
            'vehicle_pollution': [
                r'机动车|汽车|船舶|交通.*?污染|尾气|车辆',
                r'运输.*?污染|移动源|非道路.*?机械'
            ],
            'dust_pollution': [
                r'扬尘|建筑.*?扬尘|施工.*?扬尘|道路.*?扬尘',
                r'工地|拆迁|土方|裸露.*?地面'
            ],
            'agricultural_pollution': [
                r'农业.*?污染|养殖.*?污染|种植.*?污染|农村.*?污染',
                r'畜禽|农田|化肥|农药|秸秆|面源.*?污染'
            ]
        }

        # 部门识别模式
        self.department_patterns = {
            'implementation': [
                r'由(.{1,20}?)负责', r'(.{1,20}?)牵头', r'(.{1,20}?)组织实施',
                r'(.{1,20}?)统筹', r'(.{1,20}?)主管', r'委托(.{1,20}?)执行'
            ],
            'supervision': [
                r'(.{1,20}?)监督', r'(.{1,20}?)检查', r'(.{1,20}?)考核',
                r'(.{1,20}?)评估', r'第三方(.{1,20}?)监督', r'专门(.{1,20}?)监管'
            ]
        }
        
        # 预算识别模式
        self.budget_patterns = {
            'budget_mention': [
                r'预算|资金|投入|费用|成本|财政|拨款|补贴|奖励',
                r'专项.*?资金|财政.*?支持|资金.*?保障'
            ],
            'specific_amount': [
                r'(\d+(?:\.\d+)?万元)', r'(\d+(?:\.\d+)?亿元)', r'(\d+(?:\.\d+)?千万元)',
                r'资金.*?(\d+(?:\.\d+)?[万亿千]元)', r'投入.*?(\d+(?:\.\d+)?[万亿千]元)',
                r'预算.*?(\d+(?:\.\d+)?[万亿千]元)', r'补贴.*?(\d+(?:\.\d+)?元)'
            ]
        }

        # 执行相关模式
        self.execution_patterns = {
            'execution_subject_rules': [
                r'由.*?负责.*?执行', r'.*?部门.*?实施', r'建立.*?执行.*?机制',
                r'明确.*?责任.*?主体', r'制定.*?实施.*?细则'
            ],
            'single_subject': [
                r'统一.*?执行', r'专门.*?负责', r'集中.*?管理',
                r'由.*?统筹', r'.*?牵头.*?实施'
            ],
            'strict_procedure': [
                r'严格.*?执行', r'必须.*?按照', r'不得.*?变更',
                r'严禁.*?擅自', r'按时.*?完成', r'限期.*?整改'
            ],
            'punishment': [
                r'处罚|罚款|责任追究|惩罚|问责|处分',
                r'违反.*?处.*?罚款', r'未完成.*?追究.*?责任',
                r'情节严重.*?处分', r'停产.*?整顿'
            ]
        }

        # 监督相关模式
        self.supervision_patterns = {
            'internal_supervision': [
                r'自查|内部.*?监督|自我.*?监管|内部.*?检查',
                r'自行.*?监督|内部.*?考核'
            ],
            'external_supervision': [
                r'第三方.*?监督', r'专门.*?机构.*?监督', r'外部.*?监督',
                r'委托.*?监督', r'独立.*?监督', r'社会.*?监督'
            ]
        }
    
    def load_keywords_dictionaries(self):
        """加载关键词词典（基于原项目）"""
        # 尝试加载原项目词典
        self.dictionaries = {}
        
        dict_files = {
            'objective': 'Part 2.1-(1) Objective_content_lexicon.txt',
            'instrument': 'Part 2.2-(1) Instrument_lexicon.txt',
            'stopwords': 'Part 3.3 Stopwords.txt'
        }
        
        for dict_name, file_path in dict_files.items():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.dictionaries[dict_name] = [line.strip() for line in f if line.strip()]
                print(f"已加载 {dict_name} 词典，包含 {len(self.dictionaries[dict_name])} 个词汇")
            except FileNotFoundError:
                print(f"未找到词典文件: {file_path}，使用默认词典")
                self.dictionaries[dict_name] = []
    
    def score_target_dimension(self, text: str) -> float:
        """
        评分：目标维度
        0=未给出低碳发展目标
        0.5=间接给出低碳发展目标
        0.75=直接给出低碳发展目标
        1=直接给出低碳发展目标且有具体的目标值
        """
        # 检查是否有具体数值的低碳目标
        has_quantitative = False
        for pattern in self.target_patterns['quantitative_targets']:
            if re.search(pattern, text):
                has_quantitative = True
                break

        # 检查是否有直接的低碳目标
        has_direct = False
        for pattern in self.target_patterns['low_carbon_direct']:
            if re.search(pattern, text):
                has_direct = True
                break

        # 检查是否有间接的低碳目标
        has_indirect = False
        for pattern in self.target_patterns['low_carbon_indirect']:
            if re.search(pattern, text):
                has_indirect = True
                break

        # 评分逻辑
        if has_direct and has_quantitative:
            return 1.0  # 直接目标且有具体数值
        elif has_direct:
            return 0.75  # 直接给出低碳发展目标
        elif has_indirect:
            return 0.5   # 间接给出低碳发展目标
        else:
            return 0.0   # 未给出低碳发展目标
    
    def score_scope_groups(self, text: str) -> float:
        """
        评分：范围-目标群体
        0=只针对一类群体
        每增加一个目标群体，得分增加0.166
        0.5=针对所有目标群体(个人、企业、行业组织、各级政府共4个)
        """
        group_count = 0

        # 检查四类目标群体
        for group_type, patterns in self.target_group_patterns.items():
            group_found = False
            for pattern in patterns:
                if re.search(pattern, text):
                    group_found = True
                    break
            if group_found:
                group_count += 1

        # 计算得分
        if group_count >= 4:
            return 0.5  # 针对所有目标群体
        else:
            return group_count * 0.166  # 每增加一个群体得分增加0.166
    
    def score_scope_problems(self, text: str) -> float:
        """
        评分：范围-问题类型
        0=仅针对性解决一种问题
        每额外解决一种问题，得分增加0.125
        0.5=针对解决所有问题(燃煤、工业、机动车船、扬尘、农业污染防治共5类)
        """
        problem_count = 0

        # 检查五类污染防治问题
        for problem_type, patterns in self.problem_type_patterns.items():
            problem_found = False
            for pattern in patterns:
                if re.search(pattern, text):
                    problem_found = True
                    break
            if problem_found:
                problem_count += 1

        # 计算得分
        if problem_count >= 5:
            return 0.5  # 针对解决所有问题
        else:
            return problem_count * 0.125  # 每额外解决一种问题得分增加0.125
    
    def score_budget_dimension(self, text: str) -> float:
        """
        评分：预算维度
        0=没有提到预算
        0.5=提到了预算，但没有给出具体金额
        1=提到了预算，且给出具体金额
        """
        # 检查是否有具体金额
        has_specific_amount = False
        for pattern in self.budget_patterns['specific_amount']:
            if re.search(pattern, text):
                has_specific_amount = True
                break

        # 检查是否提及预算
        has_budget_mention = False
        for pattern in self.budget_patterns['budget_mention']:
            if re.search(pattern, text):
                has_budget_mention = True
                break

        # 评分逻辑
        if has_specific_amount:
            return 1.0   # 提到预算且给出具体金额
        elif has_budget_mention:
            return 0.5   # 提到预算但没有具体金额
        else:
            return 0.0   # 没有提到预算
    
    def score_execution_dimension(self, text: str) -> float:
        """
        评分：执行维度
        0=没有关于政策执行的具体说明
        0.25=有关于政策执行主体和规则的说明
        0.25=将政策分配给单一主体执行
        0.25=政策的执行程序是严格的，不允许标准或规则的变化
        0.25=对不遵守政策规定的行为者有明确的惩罚
        (四个方面得分相加，取值为0，0.25，0.5，0.75，1)
        """
        score = 0.0

        # 1. 检查执行主体和规则说明
        for pattern in self.execution_patterns['execution_subject_rules']:
            if re.search(pattern, text):
                score += 0.25
                break

        # 2. 检查单一主体执行
        for pattern in self.execution_patterns['single_subject']:
            if re.search(pattern, text):
                score += 0.25
                break

        # 3. 检查严格执行程序
        for pattern in self.execution_patterns['strict_procedure']:
            if re.search(pattern, text):
                score += 0.25
                break

        # 4. 检查惩罚措施
        for pattern in self.execution_patterns['punishment']:
            if re.search(pattern, text):
                score += 0.25
                break

        return min(score, 1.0)

    def score_supervision_dimension(self, text: str) -> float:
        """
        评分：监督维度
        0=无监督
        0.5=由政策参与主体进行内部监督
        1=由政策参与主体以外的专门机构进行监督
        """
        # 检查外部专门机构监督
        for pattern in self.supervision_patterns['external_supervision']:
            if re.search(pattern, text):
                return 1.0  # 外部专门机构监督

        # 检查内部监督
        for pattern in self.supervision_patterns['internal_supervision']:
            if re.search(pattern, text):
                return 0.5  # 内部监督

        return 0.0  # 无监督

    def calculate_policy_intensity(self, text: str, policy_id: str = None) -> Dict[str, Any]:
        """
        计算政策强度（按照您的6维度评分标准）

        Args:
            text: 政策文本
            policy_id: 政策ID

        Returns:
            包含6维度评分的结果字典
        """
        # 文本预处理
        text = self.preprocess_text(text)

        # 计算6个维度的得分
        scores = {
            'target': self.score_target_dimension(text),
            'scope_groups': self.score_scope_groups(text),
            'scope_problems': self.score_scope_problems(text),
            'budget': self.score_budget_dimension(text),
            'execution': self.score_execution_dimension(text),
            'supervision': self.score_supervision_dimension(text)
        }

        # 计算总分
        total_score = sum(scores.values())

        # 强度等级分类
        if total_score >= 4.5:
            intensity_level = '高强度'
        elif total_score >= 3.0:
            intensity_level = '中等强度'
        elif total_score >= 1.5:
            intensity_level = '低强度'
        else:
            intensity_level = '极低强度'

        # 提取详细信息用于验证
        detailed_info = self.extract_detailed_info(text)

        result = {
            'policy_id': policy_id,
            'scores': scores,
            'total_score': total_score,
            'intensity_level': intensity_level,
            'detailed_info': detailed_info
        }

        return result

    def extract_detailed_info(self, text: str) -> Dict[str, Any]:
        """提取详细信息用于验证评分结果"""
        info = {
            'target_keywords': [],
            'group_mentions': [],
            'problem_mentions': [],
            'budget_mentions': [],
            'execution_mentions': [],
            'supervision_mentions': []
        }

        # 提取目标相关关键词
        for category, patterns in self.target_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    info['target_keywords'].extend([f"{category}: {match}" for match in matches])

        # 提取群体提及
        for group_type, patterns in self.target_group_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    info['group_mentions'].extend([f"{group_type}: {match}" for match in matches])

        # 提取问题类型提及
        for problem_type, patterns in self.problem_type_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    info['problem_mentions'].extend([f"{problem_type}: {match}" for match in matches])

        # 提取预算相关
        for budget_type, patterns in self.budget_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    info['budget_mentions'].extend([f"{budget_type}: {match}" for match in matches])

        # 提取执行相关
        for exec_type, patterns in self.execution_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    info['execution_mentions'].extend([f"{exec_type}: {match}" for match in matches])

        # 提取监督相关
        for sup_type, patterns in self.supervision_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    info['supervision_mentions'].extend([f"{sup_type}: {match}" for match in matches])

        return info
    
    def preprocess_text(self, text: str) -> str:
        """文本预处理"""
        # 去除多余空白字符
        text = re.sub(r'\s+', ' ', text)
        # 去除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\s\d\.\%]', '', text)
        return text.strip()
    
    def batch_calculate_intensity(self, policy_texts: Dict[str, str]) -> pd.DataFrame:
        """批量计算政策强度"""
        results = []

        for policy_id, text in policy_texts.items():
            result = self.calculate_policy_intensity(text, policy_id)

            # 展平结果用于DataFrame
            flat_result = {
                'policy_id': policy_id,
                'target_score': result['scores']['target'],
                'scope_groups_score': result['scores']['scope_groups'],
                'scope_problems_score': result['scores']['scope_problems'],
                'budget_score': result['scores']['budget'],
                'execution_score': result['scores']['execution'],
                'supervision_score': result['scores']['supervision'],
                'total_score': result['total_score'],
                'intensity_level': result['intensity_level']
            }

            results.append(flat_result)

        return pd.DataFrame(results)

# 使用示例
if __name__ == "__main__":
    extractor = CompletePolicyExtractor()

    # 示例政策文本
    sample_text = """
    关于加强工业企业碳排放管理的通知

    一、总体目标
    到2025年，全市工业企业碳排放强度较2020年下降18%，重点行业企业碳排放总量控制在500万吨以内。
    力争实现低碳发展目标，推进绿色转型。

    二、适用范围
    本政策适用于年产值超过2000万元的工业企业、个人用户、各级政府及其环保部门、行业协会等机构。
    重点解决工业污染防治、燃煤污染治理、机动车尾气排放等问题。

    三、主要措施
    1. 设立专项资金5000万元支持企业低碳技术改造
    2. 建立严格的碳排放监管制度，必须按照规定执行
    3. 推广先进节能减排技术
    4. 对违反规定的企业处以10-50万元罚款

    四、组织实施
    市环保局负责统一组织实施，各区县政府具体执行。
    建立第三方监督评估机制，委托专业机构进行年度评估。
    """

    result = extractor.calculate_policy_intensity(sample_text, "SAMPLE_001")

    print("=== 政策强度量化评分结果 ===")
    print(f"政策ID: {result['policy_id']}")
    print(f"总分: {result['total_score']:.2f}")
    print(f"强度等级: {result['intensity_level']}")

    print(f"\n各维度得分:")
    dimension_names = {
        'target': '目标维度',
        'scope_groups': '范围-目标群体',
        'scope_problems': '范围-问题类型',
        'budget': '预算维度',
        'execution': '执行维度',
        'supervision': '监督维度'
    }

    for dim, score in result['scores'].items():
        print(f"  {dimension_names[dim]}: {score:.2f}")

    print(f"\n详细信息验证:")
    detailed = result['detailed_info']
    for info_type, items in detailed.items():
        if items:
            print(f"  {info_type}: {items[:3]}...")  # 只显示前3个

    # 批量处理示例
    print(f"\n=== 批量处理示例 ===")
    policy_texts = {
        "POLICY_001": sample_text,
        "POLICY_002": "推进节能减排工作，各企业要加强管理。设立1000万元资金支持。",
        "POLICY_003": "到2030年碳排放强度下降65%，针对所有企业、个人、政府部门和行业组织。涉及燃煤、工业、交通、扬尘、农业污染防治。财政投入50亿元。由专门机构执行，建立严格考核和第三方监督机制。对违规者处以重罚。"
    }

    results_df = extractor.batch_calculate_intensity(policy_texts)
    print("批量评分结果:")
    print(results_df.to_string(index=False, float_format='%.2f'))
