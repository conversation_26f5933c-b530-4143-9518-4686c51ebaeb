#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
不同分析方法对比
展示基础版本 vs 增强版本 vs 原项目方法的差异
"""

import pandas as pd
import time
from policy_intensity_analyzer import PolicyIntensityAnalyzer
from enhanced_policy_analyzer import EnhancedPolicyAnalyzer

def compare_analysis_methods():
    """对比不同分析方法"""
    
    # 测试用的政策文本
    test_policies = {
        "简单政策": """
        加强环保工作，各部门要重视环境保护。
        """,
        
        "中等复杂政策": """
        关于节能减排的通知：
        目标是到2025年能耗强度下降20%。
        适用于工业企业和政府机关。
        涉及工业污染和燃煤治理。
        设立专项资金1000万元。
        由环保部门负责执行，建立考核机制。
        """,
        
        "复杂政策": """
        碳达峰碳中和行动方案
        
        一、总体目标
        到2030年实现碳达峰，单位GDP碳排放强度较2005年下降65%以上。
        到2060年实现碳中和，建成清洁低碳安全高效的能源体系。
        
        二、适用范围
        本方案适用于：
        1. 全市所有工业企业、服务业企业
        2. 各级政府机关和事业单位  
        3. 个人用户和社会组织
        4. 行业协会和第三方机构
        
        三、重点任务
        1. 燃煤锅炉清洁化改造：淘汰35蒸吨以下燃煤锅炉
        2. 工业污染深度治理：钢铁、水泥、化工等重点行业实施超低排放
        3. 交通运输结构优化：推广新能源汽车，发展公共交通
        4. 建筑领域节能改造：推进既有建筑节能改造
        5. 农业绿色发展：减少化肥农药使用，发展生态农业
        
        四、保障措施
        1. 资金保障：市财政设立碳中和专项资金50亿元
        2. 对重点项目给予每吨CO2减排200元补贴
        3. 建立绿色金融支持体系
        
        五、组织实施
        1. 成立市碳达峰碳中和工作领导小组，统一组织实施
        2. 各区县政府、市直各部门按职责分工负责
        3. 建立严格的目标责任制和考核评价体系
        4. 每年开展专项督查，每季度通报进展情况
        5. 对未完成目标任务的，严肃追究责任
        6. 情节严重的，给予党纪政务处分
        
        六、监督评估
        1. 委托第三方专业机构开展年度评估
        2. 建立社会监督机制，设立举报平台
        3. 评估结果向社会公开，接受监督
        4. 建立动态调整机制，根据评估结果优化政策措施
        """
    }
    
    # 初始化不同的分析器
    print("=== 初始化分析器 ===")
    
    # 基础版分析器
    basic_analyzer = PolicyIntensityAnalyzer()
    print("✓ 基础版分析器初始化完成")
    
    # 增强版分析器
    enhanced_analyzer = EnhancedPolicyAnalyzer()
    print("✓ 增强版分析器初始化完成")
    
    # 对比分析结果
    comparison_results = []
    
    for policy_name, policy_text in test_policies.items():
        print(f"\n=== 分析政策: {policy_name} ===")
        
        # 基础版分析
        start_time = time.time()
        basic_result = basic_analyzer.analyze_policy_intensity(policy_text, policy_name)
        basic_time = time.time() - start_time
        
        # 增强版分析
        start_time = time.time()
        enhanced_result = enhanced_analyzer.calculate_policy_intensity(policy_text, policy_name)
        enhanced_time = time.time() - start_time
        
        # 收集对比数据
        comparison_data = {
            'policy_name': policy_name,
            'text_length': len(policy_text),
            
            # 基础版结果
            'basic_total_score': basic_result['total_score'],
            'basic_intensity': basic_result['intensity_level'],
            'basic_time': basic_time,
            'basic_target_score': basic_result['scores']['target'],
            'basic_budget_score': basic_result['scores']['budget'],
            'basic_execution_score': basic_result['scores']['execution'],
            'basic_supervision_score': basic_result['scores']['supervision'],
            
            # 增强版结果
            'enhanced_total_score': enhanced_result['total_score'],
            'enhanced_intensity': enhanced_result['intensity_level'],
            'enhanced_time': enhanced_time,
            'enhanced_target_score': enhanced_result['scores']['target'],
            'enhanced_budget_score': enhanced_result['scores']['budget'],
            'enhanced_execution_score': enhanced_result['scores']['execution'],
            'enhanced_supervision_score': enhanced_result['scores']['supervision'],
            
            # 增强版特有信息
            'target_confidence': enhanced_result['target_info']['confidence_score'],
            'scope_coverage': enhanced_result['scope_info']['coverage_score'],
            'analysis_method': enhanced_result['analysis_method']
        }
        
        comparison_results.append(comparison_data)
        
        # 打印详细对比
        print(f"文本长度: {len(policy_text)} 字符")
        print(f"基础版 - 总分: {basic_result['total_score']:.2f}, 等级: {basic_result['intensity_level']}, 耗时: {basic_time:.3f}s")
        print(f"增强版 - 总分: {enhanced_result['total_score']:.2f}, 等级: {enhanced_result['intensity_level']}, 耗时: {enhanced_time:.3f}s")
        
        # 详细维度对比
        print("\n维度得分对比:")
        dimensions = ['target', 'budget', 'execution', 'supervision']
        for dim in dimensions:
            basic_score = basic_result['scores'][dim]
            enhanced_score = enhanced_result['scores'][dim]
            diff = enhanced_score - basic_score
            print(f"  {dim:12}: 基础版 {basic_score:.2f} | 增强版 {enhanced_score:.2f} | 差异 {diff:+.2f}")
        
        # 增强版特有信息
        print(f"\n增强版特有信息:")
        print(f"  目标置信度: {enhanced_result['target_info']['confidence_score']:.2f}")
        print(f"  范围覆盖度: {enhanced_result['scope_info']['coverage_score']:.2f}")
        print(f"  分析方法: {enhanced_result['analysis_method']}")
        
        if enhanced_result['target_info']['specific_values']:
            print(f"  提取的具体数值: {enhanced_result['target_info']['specific_values']}")
        
        if enhanced_result['scope_info']['target_groups']:
            print(f"  识别的目标群体: {enhanced_result['scope_info']['target_groups']}")
        
        if enhanced_result['scope_info']['problem_types']:
            print(f"  识别的问题类型: {enhanced_result['scope_info']['problem_types']}")
    
    # 生成对比报告
    df_comparison = pd.DataFrame(comparison_results)
    
    print("\n" + "="*80)
    print("总体对比分析")
    print("="*80)
    
    # 准确性对比（基于复杂度）
    print("\n1. 准确性分析:")
    for _, row in df_comparison.iterrows():
        complexity = "高" if row['text_length'] > 500 else "中" if row['text_length'] > 100 else "低"
        score_diff = row['enhanced_total_score'] - row['basic_total_score']
        print(f"  {row['policy_name']} (复杂度:{complexity}): 增强版比基础版高 {score_diff:.2f} 分")
    
    # 性能对比
    print(f"\n2. 性能分析:")
    avg_basic_time = df_comparison['basic_time'].mean()
    avg_enhanced_time = df_comparison['enhanced_time'].mean()
    print(f"  基础版平均耗时: {avg_basic_time:.3f}s")
    print(f"  增强版平均耗时: {avg_enhanced_time:.3f}s")
    print(f"  性能差异: {((avg_enhanced_time - avg_basic_time) / avg_basic_time * 100):+.1f}%")
    
    # 功能对比
    print(f"\n3. 功能对比:")
    print("  基础版:")
    print("    ✓ 基于关键词匹配")
    print("    ✓ 简单正则表达式")
    print("    ✓ 基础分词")
    print("    ✗ 无置信度评估")
    print("    ✗ 无覆盖度分析")
    
    print("  增强版:")
    print("    ✓ 集成原项目词典")
    print("    ✓ LAC/jieba高级分词")
    print("    ✓ 支持ERNIE模型")
    print("    ✓ 置信度评估")
    print("    ✓ 覆盖度分析")
    print("    ✓ 更精确的数值提取")
    
    # 保存详细对比结果
    df_comparison.to_excel('method_comparison_results.xlsx', index=False)
    print(f"\n详细对比结果已保存到: method_comparison_results.xlsx")
    
    return df_comparison

def analyze_original_project_integration():
    """分析与原项目的集成情况"""
    print("\n" + "="*80)
    print("与原项目技术集成分析")
    print("="*80)
    
    integration_status = {
        "PaddlePaddle + ERNIE": {
            "status": "部分集成",
            "description": "支持加载预训练模型，但需要训练好的模型文件",
            "benefit": "可以利用深度学习提高分类准确性"
        },
        "LAC分词": {
            "status": "完全集成", 
            "description": "支持LAC分词和自定义词典加载",
            "benefit": "更准确的中文分词，特别是专业术语"
        },
        "专业词典": {
            "status": "完全集成",
            "description": "可以加载原项目的539+638个专业词汇",
            "benefit": "提高政策文本的识别准确性"
        },
        "停用词过滤": {
            "status": "完全集成",
            "description": "使用原项目的停用词表",
            "benefit": "减少噪音，提高分析质量"
        },
        "文本结构化": {
            "status": "需要改进",
            "description": "目前是简化版本，可以进一步集成Stata处理逻辑",
            "benefit": "更好地处理复杂政策文档结构"
        }
    }
    
    for tech, info in integration_status.items():
        print(f"\n{tech}:")
        print(f"  状态: {info['status']}")
        print(f"  说明: {info['description']}")
        print(f"  优势: {info['benefit']}")
    
    print(f"\n建议的改进方向:")
    print("1. 训练专门的政策强度分类模型")
    print("2. 集成更多原项目的文本预处理逻辑")
    print("3. 添加数据增强功能")
    print("4. 实现批量处理和并行计算")
    print("5. 添加模型解释性分析")

if __name__ == "__main__":
    # 运行对比分析
    comparison_df = compare_analysis_methods()
    
    # 分析集成情况
    analyze_original_project_integration()
    
    print(f"\n=== 总结 ===")
    print("增强版分析器相比基础版的主要改进:")
    print("1. 集成了原项目的专业词典和LAC分词")
    print("2. 支持ERNIE模型（如果有预训练模型）")
    print("3. 提供置信度和覆盖度评估")
    print("4. 更精确的数值和结构化信息提取")
    print("5. 保持了与您需求的完全兼容性")
