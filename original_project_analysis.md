# 原项目完整逻辑架构分析

## 项目总体目标
构建"中国低碳政策强度数据集"，覆盖2007-2022年从国家到地级市的政策文本，通过多阶段文本挖掘技术量化政策强度。

---

## 第一阶段：数据准备与文本结构化

### 1.1 原始文本处理逻辑

#### **输入**：原始政策文档（.txt格式）
- 包含完整的政策文档，如常州市"十二五"科技发展规划
- 文档结构复杂，包含多级标题和内容

#### **核心算法**：中文政策文档结构识别
```python
# 识别中文编号系统的正则表达式
regex = re.compile(r'（\d）|\d\、|一、|二、|三、|（一）|（二）|第一章|第二章...')
```

**识别的标题层级**：
- 数字编号：1、2、3、...35、
- 中文数字：一、二、三、...二十、
- 括号编号：（一）（二）（三）...（二十）
- 章节编号：第一章、第二章...第二十章
- 节编号：第一节、第二节...第二十节

#### **输出**：结构化Excel文件
- `title`列：提取的标题和子标题
- `content`列：对应的内容段落

### 1.2 Stata数据精细化处理

#### **处理逻辑**：
1. **标题层级重构**：
   - 处理"、"分隔的标题：`split title, p("。")`
   - 处理"）"分隔的标题：`split title_fine, p("）")`
   - 建立标题层级关系：`titlenew21`（主标题）、`titlenew11`（子标题）

2. **标题填充算法**：
```stata
replace titlenew21 = titlenew21[_n-1] if titlenew21 ==""
replace titlenew11 = titlenew11[_n-1] if titlenew11 =="" & new1 !=1
```

3. **分类处理**：
   - **有标题结构的政策**：city_1, 3-5, 7, 9-10
   - **无标题结构的政策**：city_2, 6, 8

### 1.3 政策目标与工具智能分离

#### **分离逻辑**：基于关键词的多层级判断

**目标识别关键词**：
- `目标`、`任务`、`指标`、`产业政策导向`

**判断层级**：
1. **标题层级**（mark1）：主标题包含目标关键词
2. **子标题层级**（mark11）：子标题包含目标关键词  
3. **内容层级**（mark111）：内容包含目标关键词

**智能决策算法**：
```stata
gen save=.
replace save=mark111 if gap==0 & gap1==0      // 仅内容有关键词
replace save=mark1 if gap !=0 & gap1==0       // 仅标题有关键词
replace save=mark11 if gap==0 & gap1!=0       // 仅子标题有关键词
replace save=mark12 if gap !=0 & gap1!=0      // 标题和子标题都有
```

**背景信息过滤**：
- 过滤词：`现状`、`意义`、`形势`、`指导思想`、`基本原则`、`背景`

---

## 第二阶段：专业词典构建

### 2.1 大规模政策文本挖掘

#### **数据来源**：
- 国家级政策文本（完整语料库，代码中未提供）
- 地级市政策文本样本

#### **文本预处理流程**：
```python
# 1. 去除标点符号
string = re.sub("[{}]+".format(punctuation), "", line)

# 2. LAC分词 + 自定义词典
lac = LAC(mode='seg')
lac.load_customization('Part 2.1-(1) Objective_content_lexicon.txt')

# 3. 停用词过滤
seg_result1 = [w for w in seg_result if w not in stopwords if len(w)>1]
```

### 2.2 专业词典体系

#### **目标词典**（539个词汇）：
- **间接目标词**：发展、推进、促进、加强、提升、改善
- **直接目标词**：目标、指标、任务、要求、标准
- **具体数值词**：%、万吨、亿元、年份、降低、减少

#### **工具词典**（638个词汇）：
- **经济工具**：补偿、电价、奖励、专项资金
- **管理工具**：部门分工、目标责任考核、重点用能单位节能管理
- **市场工具**：碳排放权交易、碳市场、配额
- **监管工具**：停业整顿、关停、关闭、问责、追究

---

## 第三阶段：机器学习分类系统

### 3.1 数据标注体系

#### **分类标签**：
- `MB_1`：政策目标相关文本
- `MB_2`：政策工具相关文本  
- `MB_3`：其他政策内容

#### **训练数据特征**：
- 每个样本包含政策文本片段和对应标签
- 文本长度变化很大（从几十字到几千字）
- 涵盖不同类型的政策文档

### 3.2 深度学习模型架构

#### **技术栈**：
- **框架**：PaddlePaddle
- **预训练模型**：ERNIE-3.0系列
  - ernie-3.0-medium-zh（主要使用）
  - ernie-3.0-base-zh、ernie-3.0-mini-zh等多个版本

#### **模型配置**：
```python
# 模型参数
max_seq_length = 128        # 最大序列长度
batch_size = 32            # 批次大小
learning_rate = 3e-5       # 学习率
epochs = 10                # 训练轮数
```

#### **训练流程**：
1. **数据预处理**：文本tokenization，序列截断/填充
2. **模型训练**：使用交叉熵损失函数
3. **早停机制**：防止过拟合
4. **模型评估**：准确率指标

### 3.3 数据增强策略

#### **增强方法**：
- **稀疏增强**（sparse_aug）：基于词汇替换
- **常规增强**（data_aug）：同义词替换、句式变换
- **剪枝优化**（prune.py）：模型压缩

---

## 第四阶段：政策强度量化（推断）

### 4.1 量化逻辑（基于代码分析）

虽然代码中没有明确的强度量化部分，但从整体架构可以推断：

#### **可能的量化维度**：
1. **目标明确性**：基于目标词典匹配程度
2. **工具丰富性**：基于工具词典覆盖度
3. **文本复杂度**：基于文档结构层级
4. **数值具体性**：基于具体数字提取

#### **强度计算方法**：
```python
# 推断的计算逻辑
policy_intensity = (
    target_score * weight_target +
    instrument_score * weight_instrument +
    structure_score * weight_structure +
    specificity_score * weight_specificity
)
```

---

## 核心技术创新点

### 1. **中文政策文档智能解析**
- 复杂的中文编号系统识别
- 多层级标题结构重构
- 政策逻辑结构自动提取

### 2. **混合技术架构**
- **Stata**：复杂数据处理和统计分析
- **Python**：文本挖掘和深度学习
- **LAC**：专业中文分词
- **ERNIE**：预训练语言模型

### 3. **专业领域适配**
- 低碳政策专业词典
- 政策文档特有的文本结构处理
- 目标与工具的智能分离

### 4. **多尺度数据覆盖**
- 时间维度：2007-2022年
- 空间维度：国家到地级市
- 内容维度：目标、工具、强度

---

## 项目的学术价值

### 1. **方法论贡献**
- 政策文本自动化分析方法
- 中文政策文档结构化处理技术
- 政策强度量化评估体系

### 2. **数据集贡献**
- 大规模低碳政策文本数据集
- 标准化的政策分类标注
- 跨时空的政策强度数据

### 3. **应用价值**
- 政策效果评估
- 政策制定参考
- 学术研究支撑

---

## 与您需求的对比

### **原项目特点**：
- 专注于**政策分类**（目标 vs 工具）
- 使用**深度学习**进行文本分类
- 构建**大规模数据集**

### **您的需求**：
- 专注于**政策强度量化**（6个维度评分）
- 需要**结构化信息提取**
- 要求**具体的赋分规则**

### **结合方案**：
原项目的技术可以作为您需求的**基础设施**：
- 使用原项目的词典和分词技术
- 利用ERNIE模型进行更精确的文本理解
- 在原项目分类基础上，添加您的6维度评分逻辑
