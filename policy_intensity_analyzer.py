#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政策强度量化分析工具
根据政策文本内容，按照6个维度对政策强度进行量化评分
"""

import re
import json
import pandas as pd
from typing import Dict, List, Tuple, Any
import jieba
from collections import defaultdict

class PolicyIntensityAnalyzer:
    """政策强度分析器"""
    
    def __init__(self, config_file: str = None):
        """初始化分析器，加载词典和规则"""
        self.config_file = config_file
        if config_file:
            self.load_config_from_file(config_file)
        else:
            self.load_dictionaries()
            self.setup_scoring_rules()

    def load_config_from_file(self, config_file: str):
        """从配置文件加载设置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        self.target_keywords = config.get('target_keywords', {})
        self.target_groups = config.get('target_groups', {})
        self.problem_types = config.get('problem_types', {})
        self.budget_keywords = config.get('budget_keywords', {})
        self.execution_keywords = config.get('execution_keywords', {})
        self.supervision_keywords = config.get('supervision_keywords', {})
        self.scoring_rules = config.get('scoring_rules', {})
        self.intensity_levels = config.get('intensity_levels', {})
        self.dimension_weights = config.get('dimension_weights', {})
        self.extraction_patterns = config.get('extraction_patterns', {})
    
    def load_dictionaries(self):
        """加载各类词典"""
        # 目标相关词典
        self.target_keywords = {
            'indirect': ['发展', '推进', '促进', '加强', '提升', '改善'],
            'direct': ['目标', '指标', '任务', '要求', '标准'],
            'specific': ['%', '万吨', '亿元', '年', '到2030年', '降低', '减少', '增长']
        }
        
        # 目标群体词典
        self.target_groups = {
            'individual': ['个人', '居民', '公民', '消费者', '用户'],
            'enterprise': ['企业', '公司', '厂家', '生产商', '经营者'],
            'organization': ['行业协会', '组织', '机构', '联盟', '商会'],
            'government': ['政府', '部门', '机关', '单位', '管理部门']
        }
        
        # 问题类型词典
        self.problem_types = {
            'coal_pollution': ['燃煤', '煤炭', '锅炉', '供热'],
            'industrial_pollution': ['工业', '生产', '制造', '化工'],
            'vehicle_pollution': ['机动车', '汽车', '船舶', '交通'],
            'dust_pollution': ['扬尘', '建筑', '施工', '道路'],
            'agricultural_pollution': ['农业', '养殖', '种植', '农村']
        }
        
        # 预算相关词典
        self.budget_keywords = {
            'mention': ['预算', '资金', '投入', '费用', '成本'],
            'specific': ['万元', '亿元', '元', '资金总额', '投资']
        }
        
        # 执行相关词典
        self.execution_keywords = {
            'subject': ['负责', '主管', '执行', '实施', '承担'],
            'rules': ['规定', '要求', '标准', '程序', '流程'],
            'strict': ['严格', '必须', '应当', '禁止', '不得'],
            'punishment': ['处罚', '罚款', '责任', '追究', '惩罚']
        }
        
        # 监督相关词典
        self.supervision_keywords = {
            'internal': ['自查', '内部监督', '自我监管'],
            'external': ['监督', '检查', '审计', '评估', '考核']
        }
    
    def setup_scoring_rules(self):
        """设置评分规则"""
        self.scoring_rules = {
            'target': {
                'none': 0,
                'indirect': 0.5,
                'direct': 0.75,
                'specific': 1.0
            },
            'scope_groups': {
                'single': 0,
                'increment': 0.166,
                'all': 0.5
            },
            'scope_problems': {
                'single': 0,
                'increment': 0.125,
                'all': 0.5
            },
            'budget': {
                'none': 0,
                'mention': 0.5,
                'specific': 1.0
            },
            'execution': {
                'none': 0,
                'subject_rules': 0.25,
                'single_subject': 0.25,
                'strict_procedure': 0.25,
                'punishment': 0.25
            },
            'supervision': {
                'none': 0,
                'internal': 0.5,
                'external': 1.0
            }
        }
    
    def extract_policy_structure(self, text: str) -> Dict[str, Any]:
        """
        从政策文本中提取结构化信息
        
        Args:
            text: 政策文本
            
        Returns:
            包含结构化信息的字典
        """
        structure = {
            'targets': [],
            'target_groups': [],
            'departments': [],
            'measures': [],
            'budget_info': [],
            'execution_info': [],
            'supervision_info': []
        }
        
        # 分句处理
        sentences = re.split(r'[。！？；]', text)
        
        for sentence in sentences:
            if not sentence.strip():
                continue
                
            # 提取目标信息
            if any(keyword in sentence for keyword in self.target_keywords['direct']):
                structure['targets'].append(sentence.strip())
            
            # 提取目标群体
            for group_type, keywords in self.target_groups.items():
                if any(keyword in sentence for keyword in keywords):
                    structure['target_groups'].append({
                        'type': group_type,
                        'content': sentence.strip()
                    })
            
            # 提取部门信息
            if '部门' in sentence or '负责' in sentence:
                structure['departments'].append(sentence.strip())
            
            # 提取具体措施
            if any(word in sentence for word in ['措施', '办法', '方案', '制度']):
                structure['measures'].append(sentence.strip())
            
            # 提取预算信息
            if any(keyword in sentence for keyword in self.budget_keywords['mention']):
                structure['budget_info'].append(sentence.strip())
            
            # 提取执行信息
            if any(keyword in sentence for keyword in self.execution_keywords['subject']):
                structure['execution_info'].append(sentence.strip())
            
            # 提取监督信息
            if any(keyword in sentence for keyword in self.supervision_keywords['external']):
                structure['supervision_info'].append(sentence.strip())
        
        return structure
    
    def score_target_dimension(self, text: str, structure: Dict) -> float:
        """评分：目标维度"""
        target_text = ' '.join(structure['targets'])
        
        # 检查是否有具体数值目标
        if any(keyword in target_text for keyword in self.target_keywords['specific']):
            if re.search(r'\d+%|\d+万吨|\d+亿元', target_text):
                return self.scoring_rules['target']['specific']
        
        # 检查是否直接给出目标
        if any(keyword in target_text for keyword in self.target_keywords['direct']):
            return self.scoring_rules['target']['direct']
        
        # 检查是否间接给出目标
        if any(keyword in target_text for keyword in self.target_keywords['indirect']):
            return self.scoring_rules['target']['indirect']
        
        return self.scoring_rules['target']['none']
    
    def score_scope_groups(self, structure: Dict) -> float:
        """评分：范围-目标群体"""
        group_types = set()
        for group_info in structure['target_groups']:
            group_types.add(group_info['type'])
        
        num_groups = len(group_types)
        if num_groups >= 4:  # 所有群体
            return self.scoring_rules['scope_groups']['all']
        elif num_groups == 1:
            return self.scoring_rules['scope_groups']['single']
        else:
            return min(num_groups * self.scoring_rules['scope_groups']['increment'], 
                      self.scoring_rules['scope_groups']['all'])
    
    def score_scope_problems(self, text: str) -> float:
        """评分：范围-问题类型"""
        problem_count = 0
        for problem_type, keywords in self.problem_types.items():
            if any(keyword in text for keyword in keywords):
                problem_count += 1
        
        if problem_count >= 5:  # 所有问题类型
            return self.scoring_rules['scope_problems']['all']
        elif problem_count == 1:
            return self.scoring_rules['scope_problems']['single']
        else:
            return min(problem_count * self.scoring_rules['scope_problems']['increment'],
                      self.scoring_rules['scope_problems']['all'])
    
    def score_budget(self, structure: Dict) -> float:
        """评分：预算维度"""
        budget_text = ' '.join(structure['budget_info'])
        
        # 检查是否有具体金额
        if re.search(r'\d+万元|\d+亿元|\d+元', budget_text):
            return self.scoring_rules['budget']['specific']
        
        # 检查是否提及预算
        if budget_text:
            return self.scoring_rules['budget']['mention']
        
        return self.scoring_rules['budget']['none']
    
    def score_execution(self, structure: Dict, text: str) -> float:
        """评分：执行维度"""
        score = 0
        execution_text = ' '.join(structure['execution_info']) + text
        
        # 执行主体和规则说明
        if (any(keyword in execution_text for keyword in self.execution_keywords['subject']) and
            any(keyword in execution_text for keyword in self.execution_keywords['rules'])):
            score += self.scoring_rules['execution']['subject_rules']
        
        # 单一主体执行
        if '统一' in execution_text or '专门' in execution_text:
            score += self.scoring_rules['execution']['single_subject']
        
        # 严格程序
        if any(keyword in execution_text for keyword in self.execution_keywords['strict']):
            score += self.scoring_rules['execution']['strict_procedure']
        
        # 惩罚措施
        if any(keyword in execution_text for keyword in self.execution_keywords['punishment']):
            score += self.scoring_rules['execution']['punishment']
        
        return min(score, 1.0)
    
    def score_supervision(self, structure: Dict, text: str) -> float:
        """评分：监督维度"""
        supervision_text = ' '.join(structure['supervision_info']) + text
        
        # 外部专门机构监督
        if any(keyword in supervision_text for keyword in self.supervision_keywords['external']):
            if '第三方' in supervision_text or '专门机构' in supervision_text:
                return self.scoring_rules['supervision']['external']
        
        # 内部监督
        if any(keyword in supervision_text for keyword in self.supervision_keywords['internal']):
            return self.scoring_rules['supervision']['internal']
        
        return self.scoring_rules['supervision']['none']
    
    def analyze_policy_intensity(self, text: str, policy_id: str = None) -> Dict[str, Any]:
        """
        分析政策强度
        
        Args:
            text: 政策文本
            policy_id: 政策ID
            
        Returns:
            政策强度分析结果
        """
        # 提取结构化信息
        structure = self.extract_policy_structure(text)
        
        # 计算各维度得分
        scores = {
            'target': self.score_target_dimension(text, structure),
            'scope_groups': self.score_scope_groups(structure),
            'scope_problems': self.score_scope_problems(text),
            'budget': self.score_budget(structure),
            'execution': self.score_execution(structure, text),
            'supervision': self.score_supervision(structure, text)
        }
        
        # 计算综合得分
        total_score = sum(scores.values())
        
        result = {
            'policy_id': policy_id,
            'structure': structure,
            'scores': scores,
            'total_score': total_score,
            'intensity_level': self.classify_intensity(total_score)
        }
        
        return result
    
    def classify_intensity(self, total_score: float) -> str:
        """根据总分对政策强度进行分类"""
        if total_score >= 4.5:
            return '高强度'
        elif total_score >= 3.0:
            return '中等强度'
        elif total_score >= 1.5:
            return '低强度'
        else:
            return '极低强度'
    
    def batch_analyze(self, policy_texts: Dict[str, str]) -> pd.DataFrame:
        """
        批量分析政策强度
        
        Args:
            policy_texts: {policy_id: policy_text} 格式的字典
            
        Returns:
            包含分析结果的DataFrame
        """
        results = []
        
        for policy_id, text in policy_texts.items():
            result = self.analyze_policy_intensity(text, policy_id)
            
            # 展平结果用于DataFrame
            flat_result = {
                'policy_id': policy_id,
                'target_score': result['scores']['target'],
                'scope_groups_score': result['scores']['scope_groups'],
                'scope_problems_score': result['scores']['scope_problems'],
                'budget_score': result['scores']['budget'],
                'execution_score': result['scores']['execution'],
                'supervision_score': result['scores']['supervision'],
                'total_score': result['total_score'],
                'intensity_level': result['intensity_level']
            }
            
            results.append(flat_result)
        
        return pd.DataFrame(results)

# 使用示例
if __name__ == "__main__":
    analyzer = PolicyIntensityAnalyzer()
    
    # 示例政策文本
    sample_text = """
    为实现2030年碳达峰目标，本政策针对工业企业、个人用户制定以下措施：
    1. 工业企业应减少碳排放30%
    2. 设立专项资金1000万元支持低碳技术
    3. 由环保部门负责执行，违反者处以10万元罚款
    4. 建立第三方监督机制，定期检查执行情况
    """
    
    result = analyzer.analyze_policy_intensity(sample_text, "POLICY_001")
    print("政策强度分析结果：")
    print(f"总分：{result['total_score']:.2f}")
    print(f"强度等级：{result['intensity_level']}")
    print("\n各维度得分：")
    for dim, score in result['scores'].items():
        print(f"{dim}: {score:.2f}")
