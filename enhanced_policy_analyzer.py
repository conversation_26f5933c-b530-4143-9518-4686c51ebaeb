#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版政策强度分析器
结合原项目的ERNIE模型和LAC分词技术
"""

import re
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import os

# 尝试导入原项目的技术栈
try:
    import paddle
    import paddle.nn.functional as F
    from paddlenlp.transformers import AutoModelForSequenceClassification, AutoTokenizer
    PADDLE_AVAILABLE = True
except ImportError:
    PADDLE_AVAILABLE = False
    print("PaddlePaddle未安装，将使用基础版本")

try:
    from LAC import LAC
    LAC_AVAILABLE = True
except ImportError:
    LAC_AVAILABLE = False
    print("LAC未安装，将使用jieba分词")
    import jieba

class EnhancedPolicyAnalyzer:
    """增强版政策分析器，结合原项目技术"""
    
    def __init__(self, config_file: str = None, model_path: str = None):
        """
        初始化分析器
        
        Args:
            config_file: 配置文件路径
            model_path: 预训练模型路径（如果使用ERNIE）
        """
        self.model_path = model_path
        self.load_config(config_file)
        self.load_original_lexicons()
        self.setup_nlp_tools()
        self.setup_classification_model()
    
    def load_config(self, config_file: str):
        """加载配置文件"""
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.config = config
        else:
            self.config = self.get_default_config()
    
    def load_original_lexicons(self):
        """加载原项目的专业词典"""
        # 尝试加载原项目的词典文件
        lexicon_files = {
            'objective_content': 'Part 2.1-(1) Objective_content_lexicon.txt',
            'objective_title': 'Part 2.1-(2) Objective_title_lexicon.txt', 
            'instrument': 'Part 2.2-(1) Instrument_lexicon.txt',
            'stopwords': 'Part 3.3 Stopwords.txt'
        }
        
        self.lexicons = {}
        for name, file_path in lexicon_files.items():
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.lexicons[name] = [line.strip() for line in f if line.strip()]
                print(f"已加载 {name} 词典，包含 {len(self.lexicons[name])} 个词汇")
            else:
                print(f"未找到词典文件: {file_path}")
                self.lexicons[name] = []
    
    def setup_nlp_tools(self):
        """设置NLP工具"""
        # 设置分词工具
        if LAC_AVAILABLE:
            self.lac = LAC(mode='seg')
            # 如果有自定义词典，加载它
            if 'objective_content' in self.lexicons and self.lexicons['objective_content']:
                custom_dict_path = 'temp_custom_dict.txt'
                with open(custom_dict_path, 'w', encoding='utf-8') as f:
                    for word in self.lexicons['objective_content']:
                        f.write(f"{word}\n")
                self.lac.load_customization(custom_dict_path, sep=None)
                os.remove(custom_dict_path)  # 清理临时文件
            print("使用LAC分词器")
        else:
            # 使用jieba作为备选
            if 'objective_content' in self.lexicons:
                for word in self.lexicons['objective_content']:
                    jieba.add_word(word)
            print("使用jieba分词器")
    
    def setup_classification_model(self):
        """设置分类模型"""
        self.model = None
        self.tokenizer = None
        
        if PADDLE_AVAILABLE and self.model_path and os.path.exists(self.model_path):
            try:
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model.eval()
                print("已加载ERNIE预训练模型")
            except Exception as e:
                print(f"模型加载失败: {e}")
                self.model = None
        else:
            print("使用基于规则的分类方法")
    
    def advanced_text_segmentation(self, text: str) -> List[str]:
        """高级文本分词，使用LAC或jieba"""
        # 预处理：去除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\s\d\.\%]', '', text)
        
        if LAC_AVAILABLE:
            seg_result = self.lac.run(text)
        else:
            seg_result = list(jieba.cut(text))
        
        # 过滤停用词
        if 'stopwords' in self.lexicons:
            stopwords = set(self.lexicons['stopwords'])
            seg_result = [word for word in seg_result if word not in stopwords and len(word) > 1]
        
        return seg_result
    
    def extract_with_ernie_model(self, text: str) -> Dict[str, float]:
        """使用ERNIE模型进行文本分类"""
        if not self.model or not self.tokenizer:
            return {}
        
        try:
            # 文本预处理
            inputs = self.tokenizer(text, return_tensors="pd", max_length=512, truncation=True)
            
            # 模型推理
            with paddle.no_grad():
                outputs = self.model(**inputs)
                probs = F.softmax(outputs.logits, axis=-1)
            
            # 假设模型输出对应不同的政策维度
            # 这里需要根据实际训练的模型调整
            dimensions = ['target', 'scope', 'budget', 'execution', 'supervision']
            scores = {}
            
            for i, dim in enumerate(dimensions):
                if i < probs.shape[1]:
                    scores[dim] = float(probs[0][i])
            
            return scores
            
        except Exception as e:
            print(f"ERNIE模型推理失败: {e}")
            return {}
    
    def enhanced_target_extraction(self, text: str) -> Dict[str, Any]:
        """增强的目标提取，结合词典和模型"""
        # 使用原项目的目标词典
        target_info = {
            'direct_targets': [],
            'indirect_targets': [],
            'specific_values': [],
            'confidence_score': 0.0
        }
        
        # 分词
        words = self.advanced_text_segmentation(text)
        
        # 基于词典的提取
        if 'objective_content' in self.lexicons:
            objective_words = set(self.lexicons['objective_content'])
            found_objectives = [word for word in words if word in objective_words]
            
            if found_objectives:
                target_info['confidence_score'] += 0.5
                
                # 区分直接和间接目标
                direct_indicators = ['目标', '指标', '任务', '要求']
                indirect_indicators = ['发展', '推进', '促进', '加强']
                
                for obj in found_objectives:
                    if any(indicator in obj for indicator in direct_indicators):
                        target_info['direct_targets'].append(obj)
                    elif any(indicator in obj for indicator in indirect_indicators):
                        target_info['indirect_targets'].append(obj)
        
        # 提取具体数值
        value_patterns = [
            r'(\d+(?:\.\d+)?%)',  # 百分比
            r'(\d+(?:\.\d+)?万吨)',  # 万吨
            r'(\d+(?:\.\d+)?亿元)',  # 亿元
            r'到(\d{4})年',  # 年份目标
        ]
        
        for pattern in value_patterns:
            matches = re.findall(pattern, text)
            target_info['specific_values'].extend(matches)
        
        if target_info['specific_values']:
            target_info['confidence_score'] += 0.3
        
        # 如果有ERNIE模型，结合模型结果
        if self.model:
            model_scores = self.extract_with_ernie_model(text)
            if 'target' in model_scores:
                target_info['confidence_score'] = (target_info['confidence_score'] + model_scores['target']) / 2
        
        return target_info
    
    def enhanced_scope_analysis(self, text: str) -> Dict[str, Any]:
        """增强的范围分析"""
        scope_info = {
            'target_groups': [],
            'problem_types': [],
            'coverage_score': 0.0
        }
        
        # 目标群体识别（使用更精确的词典）
        group_patterns = {
            'individual': ['个人', '居民', '公民', '消费者', '用户', '市民'],
            'enterprise': ['企业', '公司', '厂家', '生产商', '经营者', '单位'],
            'organization': ['行业协会', '组织', '机构', '联盟', '商会'],
            'government': ['政府', '部门', '机关', '管理部门', '职能部门']
        }
        
        for group_type, keywords in group_patterns.items():
            if any(keyword in text for keyword in keywords):
                scope_info['target_groups'].append(group_type)
        
        # 问题类型识别（基于原项目的分类）
        problem_patterns = {
            'coal_pollution': ['燃煤', '煤炭', '锅炉', '供热'],
            'industrial_pollution': ['工业', '生产', '制造', '化工'],
            'vehicle_pollution': ['机动车', '汽车', '船舶', '交通'],
            'dust_pollution': ['扬尘', '建筑', '施工', '道路'],
            'agricultural_pollution': ['农业', '养殖', '种植', '农村']
        }
        
        for problem_type, keywords in problem_patterns.items():
            if any(keyword in text for keyword in keywords):
                scope_info['problem_types'].append(problem_type)
        
        # 计算覆盖度得分
        group_coverage = len(scope_info['target_groups']) / 4.0  # 4个群体类型
        problem_coverage = len(scope_info['problem_types']) / 5.0  # 5个问题类型
        scope_info['coverage_score'] = (group_coverage + problem_coverage) / 2.0
        
        return scope_info
    
    def calculate_policy_intensity(self, text: str, policy_id: str = None) -> Dict[str, Any]:
        """计算政策强度（增强版）"""
        # 使用增强的提取方法
        target_info = self.enhanced_target_extraction(text)
        scope_info = self.enhanced_scope_analysis(text)
        
        # 计算各维度得分
        scores = {}
        
        # 目标维度得分
        if target_info['specific_values']:
            scores['target'] = 1.0
        elif target_info['direct_targets']:
            scores['target'] = 0.75
        elif target_info['indirect_targets']:
            scores['target'] = 0.5
        else:
            scores['target'] = 0.0
        
        # 范围得分
        group_count = len(scope_info['target_groups'])
        if group_count >= 4:
            scores['scope_groups'] = 0.5
        else:
            scores['scope_groups'] = group_count * 0.166
        
        problem_count = len(scope_info['problem_types'])
        if problem_count >= 5:
            scores['scope_problems'] = 0.5
        else:
            scores['scope_problems'] = problem_count * 0.125
        
        # 预算维度（增强版）
        budget_patterns = [
            r'(\d+(?:\.\d+)?万元)',
            r'(\d+(?:\.\d+)?亿元)',
            r'专项资金.*?(\d+)',
            r'财政.*?投入.*?(\d+)'
        ]
        
        budget_mentions = ['预算', '资金', '投入', '费用', '财政']
        has_budget_mention = any(word in text for word in budget_mentions)
        has_specific_amount = any(re.search(pattern, text) for pattern in budget_patterns)
        
        if has_specific_amount:
            scores['budget'] = 1.0
        elif has_budget_mention:
            scores['budget'] = 0.5
        else:
            scores['budget'] = 0.0
        
        # 执行和监督维度（保持原有逻辑但增强关键词）
        execution_score = 0.0
        supervision_score = 0.0
        
        # 执行维度分析
        execution_keywords = {
            'subject': ['负责', '主管', '执行', '实施', '承担', '组织'],
            'rules': ['规定', '要求', '标准', '程序', '流程', '制度'],
            'strict': ['严格', '必须', '应当', '禁止', '不得'],
            'punishment': ['处罚', '罚款', '责任', '追究', '惩罚']
        }
        
        for category, keywords in execution_keywords.items():
            if any(keyword in text for keyword in keywords):
                execution_score += 0.25
        
        scores['execution'] = min(execution_score, 1.0)
        
        # 监督维度分析
        if any(word in text for word in ['第三方', '专门机构', '外部监督']):
            scores['supervision'] = 1.0
        elif any(word in text for word in ['监督', '检查', '考核']):
            scores['supervision'] = 0.5
        else:
            scores['supervision'] = 0.0
        
        # 计算总分
        total_score = sum(scores.values())
        
        # 强度等级分类
        if total_score >= 4.5:
            intensity_level = '高强度'
        elif total_score >= 3.0:
            intensity_level = '中等强度'
        elif total_score >= 1.5:
            intensity_level = '低强度'
        else:
            intensity_level = '极低强度'
        
        return {
            'policy_id': policy_id,
            'target_info': target_info,
            'scope_info': scope_info,
            'scores': scores,
            'total_score': total_score,
            'intensity_level': intensity_level,
            'analysis_method': 'enhanced' if self.model else 'rule_based'
        }
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "model_config": {
                "max_length": 512,
                "batch_size": 32
            },
            "scoring_weights": {
                "target": 1.0,
                "scope_groups": 1.0,
                "scope_problems": 1.0,
                "budget": 1.0,
                "execution": 1.0,
                "supervision": 1.0
            }
        }

# 使用示例
if __name__ == "__main__":
    # 初始化增强版分析器
    analyzer = EnhancedPolicyAnalyzer(
        config_file='config.json',
        model_path='./checkpoint/'  # 如果有训练好的模型
    )
    
    # 示例分析
    sample_text = """
    为实现2030年碳达峰目标，制定本行动方案。
    目标：工业企业碳排放强度下降30%，个人用户节能20%。
    涉及燃煤锅炉改造、工业污染防治、机动车尾气治理等。
    设立专项资金5000万元。由环保部门统一执行，建立第三方监督机制。
    """
    
    result = analyzer.calculate_policy_intensity(sample_text, "ENHANCED_001")
    
    print("=== 增强版政策强度分析结果 ===")
    print(f"分析方法: {result['analysis_method']}")
    print(f"总分: {result['total_score']:.2f}")
    print(f"强度等级: {result['intensity_level']}")
    print(f"目标信息: {result['target_info']}")
    print(f"范围信息: {result['scope_info']}")
