cd "C:\...\Low_carbon_policy_intensity_code\policy texts (10 policies for coding)"
clear
*Import and merge files: "Part 2.1 Policy_instrument_content", "Part 2.1 Policy_instrument_title.dta"
import excel "Part 2.2 Policy_instrument_content.xlsx",sheet("Sheet1") firstrow
save "Part 2.2 Policy_instrument_content.dta"
import excel "Part 2.2 Policy_instrument_title.xlsx", sheet("Sheet1") firstrow clear
save "Part 2.2 Policy_instrument_title.dta"
clear

use "Part 2.2 Policy_instrument_content.dta"
merge 1:1 filename using "Part 2.2 Policy_instrument_title.dta"
drop _merge
save "Part 2.2 Policy_instrument_all.dta"

*Generate grouping variable
gen CC_instrument =0
gen MB_instrument =0
gen Composite_instrument=0
gen gap=abs(MB_freq-CC_freq)

*1. Grouping by CC_freq and MB_freq. Gap is used for composite instrument
replace CC_instrument=1 if  CC_freq>MB_freq & gap>=10
replace MB_instrument=1 if  CC_freq<MB_freq & gap>=10

*2. Group composite instruments
replace Composite_instrument=1 if CC_freq>=10 & MB_freq>=10 & gap<10

*3. Other situations
replace CC_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq>0 & MB_freq==0
replace MB_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq==0 & MB_freq>0

*4. If CC_freq and MB_freq equal to 0, consider title frequencies
replace CC_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq==0 & MB_freq==0 & CC_titlekey>MB_titlekey
replace MB_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq==0 & MB_freq==0 & CC_titlekey<MB_titlekey

*5. Remaining situation
replace CC_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq>MB_freq
replace MB_instrument=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0 & CC_freq<MB_freq

*6. Manually check  (Directly change the number from 0 to 1 in columns of CC_instrument, MB_instrument, Composite_instrument)
gen Manual=1 if CC_instrument==0 & MB_instrument==0 & Composite_instrument==0

replace CC_instrument=1 if CC_instrument==1 & Manual==1
replace MB_instrument=1 if MB_instrument==1 & Manual==1
replace Composite_instrument=1 if Composite_instrument==1 & Manual==1

*7. Recheck whether all the policies are classified into only one group.
gen sum_new= CC_instrument+MB_instrument+Composite_instrument
sum sum_new
drop sum_new

*Generate rename pattern
gen filename_new=""
replace filename_new="CC_"+ filename if CC_instrument==1
replace filename_new="MB_"+ filename if MB_instrument==1
replace filename_new="Composite_"+ filename if Composite_instrument==1

gen rename="ren "+filename+" "+ filename_new
order filename_new rename,after(filename)
export excel using "Part 2.2 Policy_instrument_Classification_FINAL.xlsx", firstrow(variables)



