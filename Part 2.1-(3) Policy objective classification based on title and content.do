cd "C:\...\Low_carbon_policy_intensity_code\policy texts (10 policies for coding)"
clear
*Import and merge files: "Part 2.1 Policy_objective_content", "Part 2.1 Policy_objective_title.dta"
import excel "Part 2.1 Policy_objective_content.xlsx",sheet("Sheet1") firstrow
save "Part 2.1 Policy_objective_content.dta"
import excel "Part 2.1 Policy_objective_title.xlsx", sheet("Sheet1") firstrow clear
save "Part 2.1 Policy_objective_title.dta"
clear

use "Part 2.1 Policy_objective_content.dta"
merge 1:1 filename using "Part 2.1 Policy_objective_title.dta"
drop _merge
save "Part 2.1 Policy_objective_all.dta"

*Generate grouping variable
gen Energy_group=0
gen Carbon_group=0
gen Capacity_group=0
gen Tech_group=0

*Calculcate the max frequency among 4 groups for each policy
egen Max=rmax(EnergyC CarbonR CapacityU Technology)

*1. Grouping policies by title frequency if all content frequencies euqal to 0.
replace Energy_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0 & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC !=0 & Title_CarbonR==0 & Title_CapacityU==0 & Title_Tech==0
replace Carbon_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0  & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC==0 & Title_CarbonR !=0 & Title_CapacityU==0 & Title_Tech==0
replace Capacity_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0  & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC==0 & Title_CarbonR==0 & Title_CapacityU !=0 & Title_Tech==0
replace Tech_group=1 if EnergyC==0 & CarbonR==0 & CapacityU==0 & Technology==0  & E_key ==0 & C_key==0 & CU_key==0 & Title_EnergyC==0 & Title_CarbonR==0 & Title_CapacityU==0 & Title_Tech !=0

*2. Grouping Carbon_reduction based on C_key (with specific quantitive carbon reduction goals)
replace Carbon_group=1 if C_key != 0

*3. Grouping Energy_conservation
replace Energy_group=1 if Carbon_group==0 & E_key !=0
replace Energy_group=1 if Carbon_group==0 & C_key==0 & E_key==0 & CU_key==0 & Max==EnergyC & Max !=0
  *trade-off between carbon_group and energy_group: carbon_group with top priority
replace Carbon_group=1 if Max==CarbonR  & Max !=0 & CarbonR !=EnergyC &Energy_group==0

*4. Grouping Capacity_utilization
replace Capacity_group=1 if Carbon_group==0 & Energy_group==0 & CU_key > 1
replace Capacity_group=1 if Carbon_group==0 & Energy_group==0 & CU_key <= 1 & Max==CapacityU & Max !=0

*5. Grouping Technology
replace Tech_group=1 if Carbon_group==0 & Energy_group==0 & Capacity_group==0 & Max==Technology & Max !=0

*6. Manual checking for policies with no group (Directly change the number from 0 to 1 in columns of EnergyC, CarbonR, CapacityU, and Technology)
gen Manual=1 if Energy_group==0 & Carbon_group==0 & Capacity_group==0 & Tech_group==0

replace Energy_group=1 if EnergyC==1 & Manual==1
replace Carbon_group=1 if CarbonR==1 & Manual==1
replace Capacity_group=1 if CapacityU==1 & Manual==1
replace Tech_group=1 if Technology==1 & Manual==1

*7. Generate new file_name
gen filename_new =""
replace filename_new="E_"+filename if Energy_group==1
replace filename_new="C_"+filename if Carbon_group==1
replace filename_new="CU_"+filename if Capacity_group==1
replace filename_new="Tech_"+filename if Tech_group==1

*8. Recheck whether all the policies are classified into only one group.
gen sum= Carbon_group+Energy_group+Capacity_group+Tech_group
sum sum
drop sum

*Generate rename pattern
gen rename="ren "+filename+" "+ filename_new
order filename_new rename,after(filename)
export excel using "Part 2.1 Policy_objective_Classification_FINAL.xlsx", firstrow(variables)







