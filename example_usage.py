#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政策强度分析工具使用示例
演示如何使用PolicyIntensityAnalyzer对政策文本进行量化分析
"""

import pandas as pd
from policy_intensity_analyzer import PolicyIntensityAnalyzer
import json

def load_policy_texts(file_path: str) -> dict:
    """
    从文件加载政策文本
    支持txt、json、excel等格式
    """
    if file_path.endswith('.json'):
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif file_path.endswith('.xlsx'):
        df = pd.read_excel(file_path)
        return dict(zip(df['policy_id'], df['policy_text']))
    elif file_path.endswith('.txt'):
        # 假设txt文件每行一个政策，格式：policy_id\tpolicy_text
        policies = {}
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if '\t' in line:
                    policy_id, text = line.strip().split('\t', 1)
                else:
                    policy_id, text = f"POLICY_{i+1:03d}", line.strip()
                policies[policy_id] = text
        return policies
    else:
        raise ValueError("不支持的文件格式")

def analyze_single_policy():
    """单个政策分析示例"""
    print("=== 单个政策分析示例 ===")
    
    analyzer = PolicyIntensityAnalyzer()
    
    # 示例政策文本
    policy_text = """
    关于加强工业企业碳排放管理的通知
    
    一、总体目标
    为实现2030年碳达峰、2060年碳中和目标，本市制定以下具体措施：
    1. 到2025年，全市工业企业碳排放强度较2020年下降18%
    2. 重点行业企业碳排放总量控制在500万吨以内
    
    二、适用范围
    本政策适用于：
    1. 年产值超过2000万元的工业企业
    2. 重点用能单位
    3. 各级政府及其环保部门
    4. 行业协会和第三方机构
    
    三、主要措施
    1. 燃煤锅炉改造：淘汰10蒸吨以下燃煤锅炉
    2. 工业污染防治：实施清洁生产技术改造
    3. 机动车污染控制：推广新能源车辆
    4. 建筑扬尘治理：加强施工现场管理
    5. 农业面源污染防治：推进生态农业发展
    
    四、资金保障
    市财政设立专项资金5000万元，用于支持企业低碳技术改造。
    对达标企业给予每吨碳减排100元的奖励。
    
    五、组织实施
    1. 市环保局负责统一组织实施
    2. 各区县政府具体执行
    3. 建立严格的考核制度，每季度检查一次
    4. 对未完成目标的企业，处以5-50万元罚款
    5. 情节严重的，责令停产整顿
    
    六、监督检查
    1. 建立第三方监督评估机制
    2. 委托专业机构进行年度评估
    3. 接受社会监督，设立举报电话
    4. 评估结果向社会公开
    """
    
    # 分析政策强度
    result = analyzer.analyze_policy_intensity(policy_text, "EXAMPLE_POLICY")
    
    # 输出结果
    print(f"政策ID: {result['policy_id']}")
    print(f"总分: {result['total_score']:.2f}")
    print(f"强度等级: {result['intensity_level']}")
    print("\n各维度详细得分:")
    
    dimension_names = {
        'target': '目标维度',
        'scope_groups': '范围-目标群体',
        'scope_problems': '范围-问题类型', 
        'budget': '预算维度',
        'execution': '执行维度',
        'supervision': '监督维度'
    }
    
    for dim, score in result['scores'].items():
        print(f"  {dimension_names[dim]}: {score:.2f}")
    
    print("\n提取的结构化信息:")
    structure = result['structure']
    print(f"  政策目标数量: {len(structure['targets'])}")
    print(f"  目标群体数量: {len(structure['target_groups'])}")
    print(f"  实施部门数量: {len(structure['departments'])}")
    print(f"  具体措施数量: {len(structure['measures'])}")
    print(f"  预算信息数量: {len(structure['budget_info'])}")
    print(f"  执行信息数量: {len(structure['execution_info'])}")
    print(f"  监督信息数量: {len(structure['supervision_info'])}")

def analyze_batch_policies():
    """批量政策分析示例"""
    print("\n=== 批量政策分析示例 ===")
    
    analyzer = PolicyIntensityAnalyzer()
    
    # 示例政策集合
    policy_texts = {
        "POLICY_001": """
        关于节能减排的指导意见。各企业应加强节能管理，政府部门负责监督。
        设立1000万元专项资金支持。对违规企业处以罚款。
        """,
        
        "POLICY_002": """
        碳达峰行动方案：到2030年碳排放强度下降65%。
        适用于所有工业企业、个人用户、政府机关和行业组织。
        涉及燃煤、工业、交通、建筑、农业等各领域污染防治。
        财政投入50亿元。由专门机构执行，建立严格考核和第三方监督机制。
        """,
        
        "POLICY_003": """
        环保倡议书。希望大家积极参与环保行动，共同保护环境。
        相关部门将加强宣传引导。
        """
    }
    
    # 批量分析
    results_df = analyzer.batch_analyze(policy_texts)
    
    # 输出结果
    print("批量分析结果:")
    print(results_df.to_string(index=False, float_format='%.2f'))
    
    # 保存结果
    results_df.to_excel('policy_intensity_results.xlsx', index=False)
    print("\n结果已保存到 policy_intensity_results.xlsx")
    
    # 统计分析
    print(f"\n统计摘要:")
    print(f"平均总分: {results_df['total_score'].mean():.2f}")
    print(f"最高分政策: {results_df.loc[results_df['total_score'].idxmax(), 'policy_id']}")
    print(f"最低分政策: {results_df.loc[results_df['total_score'].idxmin(), 'policy_id']}")
    
    print("\n强度等级分布:")
    intensity_counts = results_df['intensity_level'].value_counts()
    for level, count in intensity_counts.items():
        print(f"  {level}: {count}个政策")

def analyze_from_file():
    """从文件读取政策文本进行分析"""
    print("\n=== 从文件分析示例 ===")
    
    # 创建示例数据文件
    sample_data = {
        "POLICY_A": "制定明确的碳减排目标30%，适用于所有企业和个人，涉及工业和交通污染防治，投入资金1亿元，由环保部门严格执行，建立第三方监督。",
        "POLICY_B": "推进绿色发展，相关部门协调配合。",
        "POLICY_C": "到2025年能耗强度下降20%，针对重点企业，设立专项资金，严格考核，委托专业机构监督。"
    }
    
    # 保存为JSON文件
    with open('sample_policies.json', 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    # 从文件加载并分析
    try:
        policies = load_policy_texts('sample_policies.json')
        analyzer = PolicyIntensityAnalyzer()
        results_df = analyzer.batch_analyze(policies)
        
        print("从文件加载的政策分析结果:")
        print(results_df[['policy_id', 'total_score', 'intensity_level']].to_string(index=False))
        
    except Exception as e:
        print(f"文件分析出错: {e}")

def customize_scoring_rules():
    """自定义评分规则示例"""
    print("\n=== 自定义评分规则示例 ===")
    
    analyzer = PolicyIntensityAnalyzer()
    
    # 修改评分规则（例如：提高预算维度的权重）
    analyzer.scoring_rules['budget']['specific'] = 1.5  # 原来是1.0
    
    policy_text = "设立专项资金1000万元用于碳减排项目。"
    
    result = analyzer.analyze_policy_intensity(policy_text, "CUSTOM_POLICY")
    print(f"自定义规则下的预算得分: {result['scores']['budget']:.2f}")
    print(f"总分: {result['total_score']:.2f}")

if __name__ == "__main__":
    # 运行所有示例
    analyze_single_policy()
    analyze_batch_policies()
    analyze_from_file()
    customize_scoring_rules()
    
    print("\n=== 分析完成 ===")
    print("您可以根据需要修改词典和评分规则来适应具体的政策文本特点。")
