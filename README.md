# 政策强度量化分析工具

## 项目概述

本工具基于您的需求开发，用于对政策文本进行结构化处理和强度量化分析。与原有代码相比，本工具实现了以下核心功能：

### 原有代码 vs 新工具对比

| 功能 | 原有代码 | 新工具 |
|------|----------|--------|
| 分析维度 | 仅区分"政策目标"和"政策工具" | 6个维度：目标、范围、预算、执行、监督 |
| 评分方式 | 简单分类 | 量化评分（0-6分） |
| 结构化提取 | 基础文本分割 | 多维度信息提取 |
| 自定义能力 | 固定词典 | 可配置词典和评分规则 |
| 输出格式 | 分类结果 | 详细评分报告 |

## 核心功能

### 1. 结构化信息提取
- **政策目标**：自动识别政策目标表述
- **目标群体**：提取政策针对的人群/行业
- **实施部门**：识别负责执行的部门
- **具体措施**：提取政策的具体实施措施
- **预算信息**：识别资金投入相关内容
- **执行信息**：提取执行主体和规则
- **监督信息**：识别监督机制和程序

### 2. 六维度量化评分

#### 目标维度 (0-1分)
- 0分：未给出低碳发展目标
- 0.5分：间接给出低碳发展目标
- 0.75分：直接给出低碳发展目标
- 1分：直接给出低碳发展目标且有具体目标值

#### 范围-目标群体 (0-0.5分)
- 0分：只针对一类群体
- 每增加一个目标群体，得分增加0.166
- 0.5分：针对所有目标群体（个人、企业、行业组织、政府）

#### 范围-问题类型 (0-0.5分)
- 0分：仅针对性解决一种问题
- 每额外解决一种问题，得分增加0.125
- 0.5分：针对解决所有问题（燃煤、工业、交通、扬尘、农业污染防治）

#### 预算维度 (0-1分)
- 0分：没有提到预算
- 0.5分：提到了预算，但没有给出具体金额
- 1分：提到了预算，且给出具体金额

#### 执行维度 (0-1分)
- 0.25分：有关于政策执行主体和规则的说明
- 0.25分：将政策分配给单一主体执行
- 0.25分：政策的执行程序是严格的
- 0.25分：对不遵守政策规定的行为者有明确的惩罚

#### 监督维度 (0-1分)
- 0分：无监督
- 0.5分：由政策参与主体进行内部监督
- 1分：由政策参与主体以外的专门机构进行监督

## 安装和使用

### 环境要求
```bash
pip install pandas jieba
```

### 基本使用

#### 1. 单个政策分析
```python
from policy_intensity_analyzer import PolicyIntensityAnalyzer

# 初始化分析器
analyzer = PolicyIntensityAnalyzer()

# 分析政策文本
policy_text = "您的政策文本内容..."
result = analyzer.analyze_policy_intensity(policy_text, "POLICY_001")

print(f"总分: {result['total_score']:.2f}")
print(f"强度等级: {result['intensity_level']}")
```

#### 2. 批量分析
```python
# 准备政策文本字典
policy_texts = {
    "POLICY_001": "政策文本1...",
    "POLICY_002": "政策文本2...",
    # ...
}

# 批量分析
results_df = analyzer.batch_analyze(policy_texts)

# 保存结果
results_df.to_excel('policy_intensity_results.xlsx', index=False)
```

#### 3. 使用自定义配置
```python
# 使用配置文件
analyzer = PolicyIntensityAnalyzer(config_file='config.json')
```

### 配置文件说明

`config.json` 文件包含以下可配置项：

- **target_keywords**: 目标相关关键词
- **target_groups**: 目标群体词典
- **problem_types**: 问题类型词典
- **budget_keywords**: 预算相关关键词
- **execution_keywords**: 执行相关关键词
- **supervision_keywords**: 监督相关关键词
- **scoring_rules**: 评分规则
- **intensity_levels**: 强度等级划分
- **dimension_weights**: 维度权重

## 输出结果说明

### 单个政策分析结果
```python
{
    'policy_id': 'POLICY_001',
    'structure': {
        'targets': [...],           # 提取的目标信息
        'target_groups': [...],     # 目标群体信息
        'departments': [...],       # 实施部门信息
        'measures': [...],          # 具体措施
        'budget_info': [...],       # 预算信息
        'execution_info': [...],    # 执行信息
        'supervision_info': [...]   # 监督信息
    },
    'scores': {
        'target': 0.75,            # 各维度得分
        'scope_groups': 0.33,
        'scope_problems': 0.25,
        'budget': 1.0,
        'execution': 0.75,
        'supervision': 1.0
    },
    'total_score': 4.08,           # 总分
    'intensity_level': '中等强度'   # 强度等级
}
```

### 批量分析结果 (DataFrame)
| policy_id | target_score | scope_groups_score | ... | total_score | intensity_level |
|-----------|--------------|-------------------|-----|-------------|-----------------|
| POLICY_001| 0.75         | 0.33              | ... | 4.08        | 中等强度        |
| POLICY_002| 1.00         | 0.50              | ... | 5.25        | 高强度          |

## 自定义和扩展

### 1. 修改词典
编辑 `config.json` 文件中的相应词典部分：

```json
{
  "target_keywords": {
    "direct": ["目标", "指标", "任务", "您的自定义词汇"]
  }
}
```

### 2. 调整评分规则
```json
{
  "scoring_rules": {
    "target": {
      "specific": 1.2  // 提高具体目标的分值
    }
  }
}
```

### 3. 添加新的提取模式
```json
{
  "extraction_patterns": {
    "target_patterns": [
      "新的正则表达式模式"
    ]
  }
}
```

## 运行示例

```bash
# 运行完整示例
python example_usage.py
```

这将执行：
- 单个政策分析示例
- 批量政策分析示例
- 从文件读取分析示例
- 自定义评分规则示例

## 与原项目的集成

如果您想在原有项目基础上集成本工具：

1. 保留原有的文本预处理部分
2. 将本工具作为政策强度量化模块
3. 可以结合原有的ERNIE模型进行更精确的文本分类

## 注意事项

1. **词典完善**：根据您的具体政策文本特点，持续完善词典
2. **评分规则调整**：根据实际需求调整各维度的评分权重
3. **文本预处理**：对于格式复杂的政策文档，建议先进行预处理
4. **结果验证**：建议对部分结果进行人工验证，持续优化模型

## 技术支持

如需进一步定制或有技术问题，请提供具体的政策文本样例和需求说明。
